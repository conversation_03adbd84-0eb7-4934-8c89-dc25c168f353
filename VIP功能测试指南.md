# VIP功能测试指南

## 测试环境准备

### 1. 启动开发服务器
```bash
cd admin-vue
npm run dev
```

### 2. 确保后端服务运行
- 确保 Cloudflare Workers 部署正常
- 确保环境变量 `ADMIN_USERS` 已配置
- 确保 `tts-users` KV namespace 可访问

### 3. 准备测试数据
- 至少需要一个测试用户
- 确保有管理员权限的用户

## 功能测试步骤

### 测试1：访问VIP管理功能

1. **登录系统**
   - 访问 http://localhost:5174/
   - 使用管理员账号登录

2. **进入用户管理页面**
   - 点击"用户管理"标签
   - 确认用户列表正常显示

3. **检查VIP状态显示**
   - 查看用户列表中的"VIP状态"列
   - 确认VIP用户显示正确的类型和到期时间
   - 确认非VIP用户显示"非VIP"

4. **检查操作按钮**
   - 确认每行都有"编辑VIP"按钮
   - 按钮样式和图标正确显示

### 测试2：设置VIP功能

1. **打开编辑对话框**
   - 点击某个非VIP用户的"编辑VIP"按钮
   - 确认对话框正常打开
   - 确认显示当前用户信息

2. **设置月卡VIP**
   - 选择"设置VIP"操作类型
   - 选择"月卡"VIP类型
   - 确认到期时间自动设置为30天后
   - 点击"确认设置"
   - 确认操作成功提示
   - 确认用户列表更新

3. **设置自定义VIP**
   - 选择"设置VIP"操作类型
   - 选择"自定义"VIP类型
   - 手动设置到期时间
   - 确认操作成功

### 测试3：延长VIP功能

1. **选择已有VIP用户**
   - 点击VIP用户的"编辑VIP"按钮
   - 确认显示当前VIP状态

2. **延长VIP时间**
   - 选择"延长时间"操作类型
   - 输入延长天数（如30天）
   - 确认预览显示正确的新到期时间
   - 点击"确认延长"
   - 确认操作成功

3. **延长过期VIP**
   - 找一个已过期的VIP用户
   - 延长时间应从当前时间开始计算
   - 确认计算正确

### 测试4：移除VIP功能

1. **选择VIP用户**
   - 点击VIP用户的"编辑VIP"按钮

2. **移除VIP状态**
   - 选择"移除VIP"操作类型
   - 确认显示警告提示
   - 点击"确认移除"
   - 确认操作成功
   - 确认用户变为非VIP状态

### 测试5：表单验证

1. **必填字段验证**
   - 设置VIP时不选择类型，确认显示错误
   - 设置VIP时不选择时间，确认显示错误
   - 延长VIP时不输入天数，确认显示错误

2. **时间验证**
   - 尝试设置过去的时间，确认显示错误
   - 输入无效的延长天数，确认显示错误

3. **表单重置**
   - 切换操作类型时，确认表单正确重置
   - 关闭对话框再打开，确认表单重置

### 测试6：权限控制

1. **管理员权限测试**
   - 使用管理员账号，确认可以正常操作
   - 检查后端日志，确认权限检查通过

2. **非管理员权限测试**（如果有非管理员账号）
   - 使用非管理员账号登录
   - 确认无法访问VIP管理功能
   - 或者确认操作时返回权限错误

### 测试7：错误处理

1. **网络错误测试**
   - 断开网络连接
   - 尝试VIP操作
   - 确认显示网络错误提示

2. **服务器错误测试**
   - 输入不存在的用户名（通过开发者工具修改）
   - 确认显示用户不存在错误

3. **数据格式错误**
   - 输入无效的数据格式
   - 确认前端验证和后端验证都正常工作

## 预期结果

### 成功标准
- [ ] 所有VIP操作功能正常工作
- [ ] 表单验证正确有效
- [ ] 权限控制正常工作
- [ ] 错误处理友好清晰
- [ ] 用户界面响应流畅
- [ ] 数据更新实时同步

### 性能标准
- [ ] 对话框打开速度 < 500ms
- [ ] VIP操作响应时间 < 2s
- [ ] 用户列表刷新时间 < 3s
- [ ] 表单验证响应 < 100ms

## 常见问题排查

### 1. 对话框不显示
- 检查组件导入是否正确
- 检查模态框状态管理
- 查看浏览器控制台错误

### 2. API调用失败
- 检查网络连接
- 确认后端服务运行状态
- 检查管理员权限配置
- 查看后端日志

### 3. 表单验证不工作
- 检查表单规则配置
- 确认表单引用正确
- 查看验证错误信息

### 4. 数据不更新
- 检查API响应状态
- 确认store状态更新
- 检查组件响应式数据

## 测试报告模板

```
测试日期：____
测试人员：____
测试环境：____

功能测试结果：
- VIP设置：✅/❌
- VIP延长：✅/❌  
- VIP移除：✅/❌
- 表单验证：✅/❌
- 权限控制：✅/❌
- 错误处理：✅/❌

发现问题：
1. ____
2. ____

建议改进：
1. ____
2. ____
```
