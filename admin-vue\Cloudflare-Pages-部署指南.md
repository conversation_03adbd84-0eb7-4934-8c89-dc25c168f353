# Cloudflare Pages 部署指南

## 📋 部署概述

本项目是一个Vue 3 + TypeScript的前端应用，需要部署到Cloudflare Pages。

## 🔧 环境变量配置

### 在Cloudflare Pages中设置环境变量

1. **登录Cloudflare Dashboard**
   - 进入 [Cloudflare Dashboard](https://dash.cloudflare.com/)
   - 选择您的账户

2. **进入Pages项目设置**
   - 点击左侧菜单的 "Pages"
   - 选择您的项目（或创建新项目）
   - 点击 "Settings" 标签

3. **配置环境变量**
   - 在 "Environment variables" 部分
   - 点击 "Add variable" 添加以下变量：

### 必需的环境变量

| 变量名 | 描述 | 示例值 | 环境 |
|--------|------|--------|------|
| `VITE_API_BASE_URL` | API服务器地址 | `https://cardapi.aispeak.top` | Production |
| `VITE_API_TIMEOUT` | API请求超时时间(ms) | `15000` | Production |
| `VITE_API_RETRY_COUNT` | API重试次数 | `3` | Production |

### 环境配置示例

**生产环境 (Production):**
```
VITE_API_BASE_URL=https://cardapi.aispeak.top
VITE_API_TIMEOUT=15000
VITE_API_RETRY_COUNT=3
```

**预览环境 (Preview):**
```
VITE_API_BASE_URL=https://cardapi-staging.aispeak.top
VITE_API_TIMEOUT=10000
VITE_API_RETRY_COUNT=2
```

## 🚀 部署步骤

### 1. 连接Git仓库

1. 在Cloudflare Pages中点击 "Create a project"
2. 选择 "Connect to Git"
3. 授权并选择您的仓库
4. 选择分支（通常是 `main` 或 `master`）

### 2. 配置构建设置

**构建配置:**
- **Framework preset:** `Vue`
- **Build command:** `npm run build`
- **Build output directory:** `dist`
- **Root directory:** `admin-vue` (如果项目在子目录中)

### 3. 设置环境变量

在 "Environment variables" 部分添加上述必需的环境变量。

### 4. 部署

1. 点击 "Save and Deploy"
2. Cloudflare Pages会自动开始构建和部署
3. 构建完成后，您会获得一个 `.pages.dev` 域名

## 🔄 自动部署

### Git集成

- **自动部署:** 每次推送到主分支时自动部署
- **预览部署:** 每次创建Pull Request时创建预览环境
- **回滚:** 可以轻松回滚到之前的部署版本

### 分支策略

- `main/master` → 生产环境
- `develop` → 预览环境
- 其他分支 → 临时预览

## 🌐 自定义域名

### 添加自定义域名

1. 在项目设置中点击 "Custom domains"
2. 点击 "Set up a custom domain"
3. 输入您的域名
4. 按照指示配置DNS记录

### SSL证书

Cloudflare Pages自动提供免费的SSL证书，支持：
- 自动HTTPS重定向
- HTTP/2 和 HTTP/3 支持
- 全球CDN加速

## 🔍 监控和调试

### 构建日志

- 在项目的 "Deployments" 标签中查看构建日志
- 检查构建错误和警告
- 查看部署历史

### 环境变量验证

在应用中添加调试信息：
```javascript
console.log('API Base URL:', import.meta.env.VITE_API_BASE_URL)
console.log('Environment:', import.meta.env.MODE)
```

## ⚠️ 注意事项

### 环境变量前缀

- Vite要求环境变量必须以 `VITE_` 开头才能在客户端代码中访问
- 不要在环境变量中存储敏感信息（如API密钥），因为它们会暴露在客户端

### 构建优化

- 确保 `package.json` 中的依赖版本固定
- 使用 `.nvmrc` 文件指定Node.js版本
- 优化构建产物大小

### 缓存策略

Cloudflare Pages自动配置缓存：
- 静态资源（JS、CSS、图片）长期缓存
- HTML文件短期缓存
- API请求不缓存

## 🛠️ 故障排除

### 常见问题

1. **构建失败**
   - 检查Node.js版本兼容性
   - 确认所有依赖都已安装
   - 查看构建日志中的错误信息

2. **环境变量不生效**
   - 确认变量名以 `VITE_` 开头
   - 检查变量值是否正确设置
   - 重新部署以应用新的环境变量

3. **API请求失败**
   - 检查 `VITE_API_BASE_URL` 是否正确
   - 确认API服务器支持CORS
   - 检查网络连接和防火墙设置

### 调试技巧

1. **本地测试**
   ```bash
   # 使用生产环境变量本地测试
   npm run build
   npm run preview
   ```

2. **环境变量检查**
   ```javascript
   // 在开发者工具中检查
   console.table(import.meta.env)
   ```

## 📞 技术支持

如遇到部署问题：
1. 查看Cloudflare Pages文档
2. 检查构建日志
3. 验证环境变量配置
4. 测试API连接性

---

**更新时间:** 2025-01-26  
**适用版本:** Vue 3 + Vite + TypeScript  
**部署平台:** Cloudflare Pages
