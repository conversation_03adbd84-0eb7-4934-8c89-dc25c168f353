<script setup lang="ts">
import { RouterView } from 'vue-router'
import { useAuthStore } from '@/stores'
import { onMounted } from 'vue'
import { NConfigProvider, NMessageProvider, zhCN, dateZhCN } from 'naive-ui'

// 状态管理
const authStore = useAuthStore()

// 组件挂载时检查认证状态
onMounted(() => {
  authStore.checkAuth()
})
</script>

<template>
  <div id="app">
    <NConfigProvider :locale="zhCN" :date-locale="dateZhCN">
      <NMessageProvider>
        <router-view />
      </NMessageProvider>
    </NConfigProvider>
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100%;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 全局动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式工具类 */
.hidden-mobile {
  display: block;
}

@media (max-width: 768px) {
  .hidden-mobile {
    display: none;
  }
}

.hidden-desktop {
  display: none;
}

@media (max-width: 768px) {
  .hidden-desktop {
    display: block;
  }
}
</style>
