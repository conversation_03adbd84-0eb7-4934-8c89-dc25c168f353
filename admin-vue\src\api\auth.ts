import { httpClient } from './http'
import { APIModeManager, type APIMode } from '@/services/apiModeManager'
import { APIRouter } from '@/services/apiRouter'
import type { AuthRequest, AuthResponse } from '@/types'

// 认证API服务
export class AuthService {
  // 登录 (支持指定API模式)
  static async login(authData: AuthRequest, apiMode?: APIMode): Promise<AuthResponse> {
    try {
      // 如果指定了API模式，保存用户选择
      if (apiMode) {
        APIModeManager.setMode(apiMode, true)
      }

      // 获取当前API模式
      const currentMode = APIModeManager.getCurrentMode()
      console.log(`使用 ${APIModeManager.getModeName(currentMode)} 进行登录`)

      if (currentMode === 'new') {
        return await this.loginWithNewAPI(authData)
      } else {
        return await this.loginWithLegacyAPI(authData)
      }
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : '登录失败')
    }
  }

  // 使用新API登录
  private static async loginWithNewAPI(authData: AuthRequest): Promise<AuthResponse> {
    if (!authData.username || !authData.password) {
      throw new Error('新API模式需要用户名和密码')
    }

    const endpoint = APIRouter.getEndpoint('AUTH_LOGIN')
    APIRouter.logAPICall('AUTH_LOGIN', endpoint)
    const response = await httpClient.post<any>(endpoint, {
      username: authData.username,
      password: authData.password
    }) as AuthResponse

    console.log('新API响应数据:', response)

    // 处理新API响应
    if (response.access_token || response.token) {
      console.log('新API登录成功')

      // 标准化响应格式
      const standardResponse: AuthResponse = {
        success: true,
        token: response.access_token || response.token,
        refreshToken: response.refresh_token || response.refreshToken,
        expiresAt: response.expires_in ? Date.now() + (response.expires_in * 1000) : response.expiresAt,
        user: response.username ? { username: response.username } : response.user
      }

      this.saveAuthData(standardResponse, authData.username)
      return standardResponse
    } else {
      throw new Error('新API响应中没有找到token字段')
    }
  }

  // 使用旧API登录
  private static async loginWithLegacyAPI(authData: AuthRequest): Promise<AuthResponse> {
    if (!authData.auth_code) {
      throw new Error('兼容模式需要授权码')
    }

    const endpoint = APIRouter.getEndpoint('AUTH_LOGIN')
    APIRouter.logAPICall('AUTH_LOGIN', endpoint)
    const response = await httpClient.post<any>(endpoint, {
      auth_code: authData.auth_code,
      username: authData.username || 'admin'
    }) as AuthResponse

    console.log('旧API响应数据:', response)

    if (response.success && response.token) {
      console.log('旧API登录成功')
      this.saveAuthData(response, authData.username || 'admin')
      return response
    } else {
      throw new Error('授权码无效或登录失败')
    }
  }

  // 保存认证数据
  private static saveAuthData(response: AuthResponse, username?: string): void {
    // 保存access token (支持多种字段名)
    const token = response.token || response.access_token
    if (token) {
      localStorage.setItem('auth_token', token)
      console.log('Token已保存到localStorage')
    }

    // 保存refresh token (支持多种字段名)
    const refreshToken = response.refreshToken || response.refresh_token
    if (refreshToken) {
      localStorage.setItem('refresh_token', refreshToken)
      console.log('RefreshToken已保存到localStorage')
    }

    // 保存用户名 (支持多种格式)
    const finalUsername = username || response.user?.username || response.username
    if (finalUsername) {
      localStorage.setItem('username', finalUsername)
      console.log('用户名已保存:', finalUsername)
    }

    // 保存过期时间
    if (response.expiresAt) {
      localStorage.setItem('token_expires_at', response.expiresAt.toString())
      console.log('Token过期时间已保存:', new Date(response.expiresAt))
    } else {
      // 如果没有过期时间，设置默认2小时过期
      const defaultExpiry = Date.now() + (2 * 60 * 60 * 1000) // 2小时
      localStorage.setItem('token_expires_at', defaultExpiry.toString())
      console.log('使用默认Token过期时间:', new Date(defaultExpiry))
    }
  }

  // 登出
  static logout(): void {
    localStorage.removeItem('auth_token')
    localStorage.removeItem('username')
    localStorage.removeItem('token_expires_at')
  }

  // 检查认证状态
  static isAuthenticated(): boolean {
    const token = localStorage.getItem('auth_token')
    const expiresAt = localStorage.getItem('token_expires_at')
    
    if (!token || !expiresAt) {
      return false
    }
    
    // 检查token是否过期
    const now = Date.now()
    const expiry = parseInt(expiresAt)
    
    if (now >= expiry) {
      this.logout()
      return false
    }
    
    return true
  }

  // 获取当前用户信息
  static getCurrentUser(): { username: string; token: string } | null {
    const token = localStorage.getItem('auth_token')
    const username = localStorage.getItem('username')
    
    if (!token || !this.isAuthenticated()) {
      return null
    }
    
    return {
      username: username || 'admin',
      token
    }
  }

  // 获取token
  static getToken(): string | null {
    if (!this.isAuthenticated()) {
      return null
    }
    return localStorage.getItem('auth_token')
  }
}
