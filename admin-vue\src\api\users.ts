import { httpClient } from './http'
import { APIRouter } from '@/services/apiRouter'
import type { User, UsageData, PaginationParams, StatsData, SystemStats, UpdateVipRequest, UpdateVipResponse, UsersApiResponse, PaginationResponse } from '@/types'

// 用户API服务
export class UserService {
  // 获取用户列表 (使用API路由器)
  static async getUsers(params?: { page?: number; limit?: number; search?: string }): Promise<User[]> {
    try {
      // 构建查询参数
      const queryParams = new URLSearchParams()
      if (params?.page) queryParams.append('page', params.page.toString())
      if (params?.limit) queryParams.append('limit', params.limit.toString())
      if (params?.search) queryParams.append('search', params.search)

      const endpoint = APIRouter.getEndpoint('ADMIN_USERS')
      const url = `${endpoint}${queryParams.toString() ? '?' + queryParams.toString() : ''}`
      APIRouter.logAPICall('ADMIN_USERS', url)

      const response = await httpClient.get<any>(url)
      console.log('用户列表API响应:', response)

      // 处理新API响应格式
      if (response.users || response.success) {
        const users = response.users || response.data?.users || []
        console.log('原始用户数据:', users)

        // 标准化用户数据格式
        const standardizedUsers = users.map((user: any) => {
          // 处理时间字段 - 支持ISO字符串和时间戳
          const parseTime = (timeValue: string | number | undefined): number => {
            if (!timeValue) return 0
            if (typeof timeValue === 'string') {
              return new Date(timeValue).getTime()
            }
            return timeValue
          }

          const standardUser: User = {
            username: user.username,
            email: user.email,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,

            // 新的VIP结构
            vip: user.vip ? {
              type: user.vip.type,
              expireAt: user.vip.expireAt || 0,
              quotaChars: user.vip.quotaChars,
              usedChars: user.vip.usedChars,
              isExpired: user.vip.isExpired
            } : undefined,

            // 新的用量结构
            usage: user.usage ? {
              totalChars: user.usage.totalChars || 0,
              monthlyChars: user.usage.monthlyChars || 0
            } : undefined,

            // 向后兼容字段
            createAt: parseTime(user.createdAt || user.created_at),
            quota: {
              daily: user.vip?.quotaChars || 0,
              used: user.vip?.usedChars || user.usage?.totalChars || 0,
              resetAt: 0
            }
          }

          console.log('标准化用户数据:', standardUser)
          return standardUser
        })

        return standardizedUsers
      }

      throw new Error(response.message || response.error || '获取用户列表失败')
    } catch (error) {
      console.error('API调用失败:', error)

      throw new Error(error instanceof Error ? error.message : '获取用户列表失败')
    }
  }

  // 获取用量统计数据 (使用专用用量接口)
  static async getUsageData(params: PaginationParams & { search?: string, page?: number } = {}): Promise<{
    users: UsageData[]
    hasMore: boolean
    nextCursor?: string
    stats: StatsData
  }> {
    try {
      const queryParams = new URLSearchParams()

      // 使用标准分页参数
      const limit = Math.min(params.limit || 100, 1000)
      queryParams.append('limit', limit.toString())

      if (params.search) {
        queryParams.append('search', params.search)
      }

      // 使用专用的用量统计接口
      const endpoint = APIRouter.getEndpoint('USER_QUOTA')
      const url = `${endpoint}${queryParams.toString() ? '?' + queryParams.toString() : ''}`
      APIRouter.logAPICall('USER_QUOTA', url)
      console.log('请求用量数据参数:', params)

      const response = await httpClient.get<any>(url)
      console.log('用量数据API响应:', response)

      // 处理用量数据响应格式
      // 响应格式：{ users: [...] } 或直接是数组
      let usersArray = []
      if ((response as any).users && Array.isArray((response as any).users)) {
        usersArray = (response as any).users
      } else if (Array.isArray(response)) {
        usersArray = response
      } else {
        throw new Error('用量数据响应格式不正确')
      }

      // 标准化用户数据格式
      const standardizedUsers = this.standardizeUsageData(usersArray)

      // 计算统计数据
      const stats = this.calculateUsageStats(standardizedUsers)

      return {
        users: standardizedUsers,
        hasMore: false, // 用量接口通常返回所有数据
        nextCursor: undefined,
        stats: stats
      }
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : '获取用量数据失败')
    }
  }

  // 获取系统统计数据 (使用API路由器)
  static async getSystemStats(): Promise<SystemStats> {
    try {
      const endpoint = APIRouter.getEndpoint('USER_STATS')
      APIRouter.logAPICall('USER_STATS', endpoint)
      const response = await httpClient.get<any>(endpoint)
      console.log('系统统计API响应:', response)

      // 适配实际的统计API响应格式：{ stats: { totalUsers, totalCharsUsed, monthlyCharsUsed, vipUsersCount }, timestamp }
      if ((response as any).stats) {
        const stats = (response as any).stats
        return {
          users: {
            total_users: String(stats.totalUsers || 0),
            active_vip_users: String(stats.vipUsersCount || 0),
            new_users_7d: '0',
            new_users_30d: '0'
          },
          tasks: {
            total_tasks: '0',
            completed_tasks: '0',
            failed_tasks: '0',
            processing_tasks: '0',
            tasks_24h: '0',
            tasks_7d: '0'
          },
          cards: {
            total_cards: '0',
            used_cards: '0',
            unused_cards: '0',
            new_cards_7d: '0'
          },
          taskTrend: [],
          timestamp: (response as any).timestamp || new Date().toISOString()
        }
      }

      throw new Error(response.message || response.error || '获取系统统计失败')
    } catch (error) {
      console.error('系统统计API失败:', error)
      throw new Error(error instanceof Error ? error.message : '获取系统统计失败')
    }
  }

  // 获取全局统计数据 (直接使用统计API响应)
  static async getGlobalStats(): Promise<StatsData> {
    try {
      const endpoint = APIRouter.getEndpoint('USER_STATS')
      APIRouter.logAPICall('USER_STATS', endpoint)
      const response = await httpClient.get<any>(endpoint)
      console.log('全局统计API响应:', response)

      // 直接使用统计API的响应格式：{ stats: { totalUsers, totalCharsUsed, monthlyCharsUsed, vipUsersCount }, timestamp }
      if ((response as any).stats) {
        const stats = (response as any).stats
        return {
          totalUsers: stats.totalUsers || 0,
          totalCharsUsed: stats.totalCharsUsed || 0,
          monthlyCharsUsed: stats.monthlyCharsUsed || 0,
          vipUsersCount: stats.vipUsersCount || 0,
          newUsers7d: 0,
          newUsers30d: 0,
          totalTasks: 0,
          completedTasks: 0,
          failedTasks: 0
        }
      }

      throw new Error('统计数据响应格式不正确')
    } catch (error) {
      console.error('全局统计API失败:', error)
      // 直接返回默认统计数据，避免重复请求
      return {
        totalUsers: 0,
        totalCharsUsed: 0,
        monthlyCharsUsed: 0,
        vipUsersCount: 0
      }
    }
  }

  // 标准化用量数据格式 (适配正确的API响应格式)
  private static standardizeUsageData(users: any[]): UsageData[] {
    return users.map((user: any) => {
      const parseTime = (timeValue: string | number | undefined): number => {
        if (!timeValue) return 0
        if (typeof timeValue === 'string') {
          return new Date(timeValue).getTime()
        }
        return timeValue
      }

      // 适配正确的响应格式：{ username, usage: { totalChars, monthlyChars, monthlyResetAt }, createdAt, vip: { expireAt, type } }
      const usage_stats = user.usage || {}
      const vip_info = user.vip || {}

      const standardUser: UsageData = {
        username: user.username,
        email: user.email || '',
        created_at: user.created_at,
        createdAt: parseTime(user.createdAt),
        updatedAt: user.updatedAt || Date.now(),
        vip_info: {
          type: vip_info.type || '',
          expireAt: vip_info.expireAt || 0,
          quotaChars: vip_info.quotaChars || 0,
          usedChars: usage_stats.totalChars || 0,
          remainingChars: vip_info.remainingChars || 0,
          usagePercentage: vip_info.usagePercentage || 0,
          isExpired: vip_info.expireAt ? vip_info.expireAt < Date.now() : false
        },
        usage_stats: {
          totalChars: usage_stats.totalChars || 0,
          monthlyChars: usage_stats.monthlyChars || 0,
          monthlyResetAt: usage_stats.monthlyResetAt || 0
        },
        // 向后兼容
        usage: usage_stats,
        vip: vip_info
      }

      return standardUser
    })
  }

  // 计算用量统计数据（保留用于向后兼容）
  private static calculateUsageStats(users: UsageData[]): StatsData {
    let totalCharsUsed = 0
    let monthlyCharsUsed = 0
    let vipUsersCount = 0

    users.forEach(user => {
      // 优先使用新格式，回退到旧格式
      const usage = user.usage_stats || user.usage || {}
      const vip = user.vip_info || user.vip || {}

      totalCharsUsed += (usage as any).totalChars || 0
      monthlyCharsUsed += (usage as any).monthlyChars || 0

      if (vip && (vip as any).expireAt > Date.now()) {
        vipUsersCount++
      }
    })

    return {
      totalUsers: users.length,
      totalCharsUsed,
      monthlyCharsUsed,
      vipUsersCount
    }
  }

  // 更新用户VIP状态 (使用API路由器)
  static async updateUserVip(username: string, vipData: UpdateVipRequest): Promise<UpdateVipResponse> {
    try {
      const endpoint = APIRouter.getEndpoint('ADMIN_USER_VIP')
      const url = `${endpoint}/${username}/vip`
      APIRouter.logAPICall('ADMIN_USER_VIP', url)
      const response = await httpClient.put<UpdateVipResponse>(url, vipData)

      if (response.success) {
        return response
      }

      throw new Error(response.message || response.error || 'VIP状态更新失败')
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : 'VIP状态更新失败')
    }
  }

  // 获取用户详细信息 (使用API路由器)
  static async getUserDetail(username: string): Promise<any> {
    try {
      const endpoint = APIRouter.getEndpoint('ADMIN_USER_DETAIL')
      const url = `${endpoint}/${username}`
      APIRouter.logAPICall('ADMIN_USER_DETAIL', url)
      const response = await httpClient.get<any>(url)

      if (response.success || (response as any).user) {
        return (response as any).user || response.data?.user || response
      }

      throw new Error(response.message || response.error || '获取用户详情失败')
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : '获取用户详情失败')
    }
  }
}
