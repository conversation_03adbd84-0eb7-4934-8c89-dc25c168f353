@import './base.css';

/* 全局应用样式 - 确保占满全屏 */
#app {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  font-weight: normal;
  overflow: hidden;
}

/* 链接样式 */
a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

/* 确保body也占满全屏 */
body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  /* 优化移动端滚动 */
  -webkit-overflow-scrolling: touch;
  /* 防止移动端缩放 */
  touch-action: manipulation;
}

/* 全局响应式优化 */
* {
  box-sizing: border-box;
}

/* 确保触摸目标足够大 */
button,
.n-button,
a,
input,
select,
textarea {
  min-height: 44px;
  min-width: 44px;
}

/* 移动端优化 */
@media (max-width: 768px) {
  /* 更大的触摸目标 */
  button,
  .n-button {
    min-height: 48px;
    padding: 12px 16px;
  }

  /* 输入框优化 */
  input,
  select,
  textarea {
    min-height: 48px;
    font-size: 16px; /* 防止iOS缩放 */
  }
}

/* 小屏幕设备 */
@media (max-width: 480px) {
  /* 更大的触摸目标 */
  button,
  .n-button {
    min-height: 52px;
    padding: 14px 18px;
  }

  input,
  select,
  textarea {
    min-height: 52px;
    font-size: 16px;
  }
}
