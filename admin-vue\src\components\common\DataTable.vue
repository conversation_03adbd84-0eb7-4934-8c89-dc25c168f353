<template>
  <div class="data-table">
    <n-data-table
      :columns="tableColumns"
      :data="data"
      :loading="loading"
      :pagination="paginationConfig"
      :row-key="rowKeyFunction"
      :checked-row-keys="checkedRowKeys"
      :on-update:checked-row-keys="handleCheck"
      :scroll-x="scrollX"
      striped
      size="medium"
      flex-height
      style="min-height: 600px; max-height: 80vh;"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, type PropType } from 'vue'
import { NDataTable, type DataTableColumns, type PaginationProps } from 'naive-ui'
import type { TableColumn } from '@/types'

// 定义RowKey类型
type RowKey = string | number

// Props定义
const props = defineProps({
  // 表格数据
  data: {
    type: Array as PropType<any[]>,
    default: () => []
  },
  // 列配置
  columns: {
    type: Array as PropType<TableColumn[]>,
    required: true
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  },
  // 行键
  rowKey: {
    type: [String, Function] as PropType<string | ((row: any) => string)>,
    default: 'id'
  },
  // 是否可选择
  checkable: {
    type: Boolean,
    default: false
  },
  // 选中的行键
  checkedRowKeys: {
    type: Array as PropType<RowKey[]>,
    default: () => []
  },
  // 分页配置
  pagination: {
    type: [Object, Boolean] as PropType<PaginationProps | false>,
    default: () => ({
      page: 1,
      pageSize: 20,
      showSizePicker: true,
      pageSizes: [10, 20, 50, 100],
      showQuickJumper: true
    })
  },
  // 水平滚动宽度
  scrollX: {
    type: Number,
    default: undefined
  }
})

// Emits定义
const emit = defineEmits<{
  'update:checkedRowKeys': [keys: RowKey[]]
  'update:page': [page: number]
  'update:pageSize': [pageSize: number]
}>()

// 获取嵌套属性值的辅助函数
const getNestedValue = (obj: any, path: string) => {
  return path.split('.').reduce((current, key) => current?.[key], obj)
}

// 创建排序函数
const createSorter = (key: string) => {
  return (row1: any, row2: any) => {
    console.log(`排序列: ${key}`, { row1, row2 }) // 调试信息

    const val1 = getNestedValue(row1, key)
    const val2 = getNestedValue(row2, key)

    console.log(`排序值: ${key}`, { val1, val2 }) // 调试信息

    // 处理null/undefined值
    if (val1 == null && val2 == null) return 0
    if (val1 == null) return 1
    if (val2 == null) return -1

    // 处理不同数据类型的排序
    if (typeof val1 === 'number' && typeof val2 === 'number') {
      const result = val1 - val2
      console.log(`数字排序结果: ${result}`) // 调试信息
      return result
    }

    if (typeof val1 === 'string' && typeof val2 === 'string') {
      const result = val1.localeCompare(val2, 'zh-CN')
      console.log(`字符串排序结果: ${result}`) // 调试信息
      return result
    }

    // 处理日期类型
    if (val1 instanceof Date && val2 instanceof Date) {
      const result = val1.getTime() - val2.getTime()
      console.log(`日期排序结果: ${result}`) // 调试信息
      return result
    }

    // 处理时间戳字符串
    if (typeof val1 === 'string' && typeof val2 === 'string') {
      const date1 = new Date(val1)
      const date2 = new Date(val2)
      if (!isNaN(date1.getTime()) && !isNaN(date2.getTime())) {
        const result = date1.getTime() - date2.getTime()
        console.log(`时间戳字符串排序结果: ${result}`) // 调试信息
        return result
      }
    }

    // 默认字符串比较
    const result = String(val1 || '').localeCompare(String(val2 || ''), 'zh-CN')
    console.log(`默认字符串排序结果: ${result}`) // 调试信息
    return result
  }
}

// 计算属性 - 表格列配置
const tableColumns = computed((): DataTableColumns => {
  const columns: DataTableColumns = props.columns.map(col => {
    const column: any = {
      key: col.key,
      title: col.title,
      width: col.width,
      minWidth: col.minWidth,
      fixed: col.fixed,
      render: col.render || ((row: any) => row[col.key])
    }

    // 如果列可排序，添加排序器
    if (col.sortable) {
      console.log(`配置排序列: ${col.key}`) // 调试信息
      column.sorter = createSorter(col.key)
    }

    return column
  })

  // 如果可选择，添加选择列
  if (props.checkable) {
    columns.unshift({
      type: 'selection'
    })
  }

  return columns
})

// 计算属性 - 行键函数
const rowKeyFunction = computed(() => {
  if (typeof props.rowKey === 'function') {
    return props.rowKey
  }
  // 将字符串转换为函数
  return (row: any) => row[props.rowKey as string]
})

// 计算属性 - 分页配置
const paginationConfig = computed(() => {
  if (props.pagination === false) {
    return false
  }

  return {
    ...props.pagination,
    prefix: (info: { itemCount?: number }) => `共 ${info.itemCount || 0} 条`,
    onUpdatePage: (page: number) => emit('update:page', page),
    onUpdatePageSize: (pageSize: number) => emit('update:pageSize', pageSize)
  }
})

// 处理选择变化
const handleCheck = (keys: RowKey[]) => {
  emit('update:checkedRowKeys', keys)
}
</script>

<style scoped>
.data-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  width: 100%;
}

:deep(.n-data-table-th) {
  background: #f8fafc !important;
  font-weight: 600;
  padding: 12px 16px;
}

:deep(.n-data-table-tr:hover) {
  background: #f1f5f9 !important;
}

:deep(.n-data-table-td) {
  border-bottom: 1px solid #e2e8f0;
  padding: 12px 16px;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .data-table {
    border-radius: 6px;
  }

  :deep(.n-data-table-th),
  :deep(.n-data-table-td) {
    padding: 8px 12px;
    font-size: 14px;
  }

  /* 移动端表格滚动优化 */
  :deep(.n-data-table-base-table) {
    min-width: 100%;
  }

  /* 移动端分页优化 */
  :deep(.n-pagination) {
    justify-content: center;
    flex-wrap: wrap;
    gap: 8px;
  }

  :deep(.n-pagination-item) {
    min-width: 36px;
    min-height: 36px;
  }
}

@media (max-width: 480px) {
  :deep(.n-data-table-th),
  :deep(.n-data-table-td) {
    padding: 6px 8px;
    font-size: 13px;
  }

  /* 超小屏幕分页优化 */
  :deep(.n-pagination) {
    font-size: 12px;
  }

  :deep(.n-pagination-item) {
    min-width: 32px;
    min-height: 32px;
  }

  /* 隐藏部分分页信息以节省空间 */
  :deep(.n-pagination-prefix) {
    display: none;
  }
}

/* 大屏幕优化 */
@media (min-width: 1200px) {
  :deep(.n-data-table-th),
  :deep(.n-data-table-td) {
    padding: 16px 20px;
  }
}
</style>
