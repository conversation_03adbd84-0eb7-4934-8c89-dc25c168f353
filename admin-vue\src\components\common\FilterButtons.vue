<template>
  <div class="filter-buttons">
    <div class="filter-label">{{ label }}</div>
    <n-button-group>
      <n-button
        v-for="option in options"
        :key="option.value"
        :type="activeValue === option.value ? 'primary' : 'default'"
        :secondary="activeValue !== option.value"
        size="small"
        @click="handleSelect(option.value)"
      >
        {{ option.label }}
      </n-button>
    </n-button-group>
  </div>
</template>

<script setup lang="ts">
import { NButton, NButtonGroup } from 'naive-ui'

// 筛选选项类型
interface FilterOption {
  value: string
  label: string
}

// Props定义
const props = defineProps({
  // 当前选中值
  modelValue: {
    type: String,
    required: true
  },
  // 筛选选项
  options: {
    type: Array as () => FilterOption[],
    required: true
  },
  // 标签
  label: {
    type: String,
    default: '筛选条件：'
  }
})

// Emits定义
const emit = defineEmits<{
  'update:modelValue': [value: string]
  'change': [value: string]
}>()

// 当前选中值
const activeValue = computed(() => props.modelValue)

// 处理选择
const handleSelect = (value: string) => {
  emit('update:modelValue', value)
  emit('change', value)
}
</script>

<script lang="ts">
import { computed } from 'vue'
</script>

<style scoped>
.filter-buttons {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.filter-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
  white-space: nowrap;
}

:deep(.n-button-group) {
  border-radius: 6px;
  overflow: hidden;
}

:deep(.n-button) {
  font-size: 13px;
  padding: 0 12px;
  height: 32px;
  transition: all 0.2s ease;
}

:deep(.n-button--primary-type) {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-color: #3b82f6;
}

:deep(.n-button--default-type) {
  border-color: #d1d5db;
  color: #6b7280;
}

:deep(.n-button--default-type:hover) {
  border-color: #3b82f6;
  color: #3b82f6;
}

/* 响应式 */
@media (max-width: 768px) {
  .filter-buttons {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  :deep(.n-button-group) {
    width: 100%;
  }
  
  :deep(.n-button) {
    flex: 1;
  }
}
</style>
