<template>
  <div class="page-loading">
    <n-spin :size="size" :show="show">
      <div class="loading-content">
        <div class="loading-icon">
          <n-icon :component="RefreshOutline" />
        </div>
        <div class="loading-text">{{ text }}</div>
      </div>
    </n-spin>
  </div>
</template>

<script setup lang="ts">
import { NSpin, NIcon } from 'naive-ui'
import { RefreshOutline } from '@vicons/ionicons5'

// Props定义
const props = defineProps({
  // 是否显示加载
  show: {
    type: Boolean,
    default: true
  },
  // 加载文本
  text: {
    type: String,
    default: '加载中...'
  },
  // 尺寸
  size: {
    type: String as () => 'small' | 'medium' | 'large',
    default: 'medium'
  }
})
</script>

<style scoped>
.page-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  width: 100%;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.loading-icon {
  font-size: 24px;
  color: #3b82f6;
  animation: rotate 1s linear infinite;
}

.loading-text {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

:deep(.n-spin-content) {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
