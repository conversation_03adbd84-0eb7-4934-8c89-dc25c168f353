<template>
  <n-button
    v-bind="$attrs"
    :class="[
      'responsive-button',
      {
        'mobile-optimized': isMobile,
        'touch-optimized': isTouchDevice
      }
    ]"
    :style="buttonStyle"
    @click="handleClick"
    @touchstart="handleTouchStart"
    @touchend="handleTouchEnd"
  >
    <slot />
  </n-button>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { NButton } from 'naive-ui'
import { useResponsive, isTouchDevice } from '@/utils/responsive'

// Props
interface Props {
  // 是否为主要按钮
  primary?: boolean
  // 自定义最小尺寸
  minWidth?: string
  minHeight?: string
  // 是否全宽（移动端）
  fullWidthOnMobile?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  primary: false,
  fullWidthOnMobile: false
})

// Emits
const emit = defineEmits<{
  click: [event: MouseEvent]
  touchstart: [event: TouchEvent]
  touchend: [event: TouchEvent]
}>()

// 响应式状态
const { isMobile, isXs } = useResponsive()
const isPressed = ref(false)

// 计算样式
const buttonStyle = computed(() => {
  const style: Record<string, string> = {}
  
  // 基础最小尺寸
  if (isXs.value) {
    style.minWidth = props.minWidth || '48px'
    style.minHeight = props.minHeight || '52px'
    style.padding = '14px 18px'
  } else if (isMobile.value) {
    style.minWidth = props.minWidth || '44px'
    style.minHeight = props.minHeight || '48px'
    style.padding = '12px 16px'
  } else {
    style.minWidth = props.minWidth || '40px'
    style.minHeight = props.minHeight || '44px'
  }
  
  // 移动端全宽
  if (props.fullWidthOnMobile && isMobile.value) {
    style.width = '100%'
  }
  
  // 触摸反馈
  if (isPressed.value && isTouchDevice()) {
    style.transform = 'scale(0.98)'
  }
  
  return style
})

// 事件处理
const handleClick = (event: MouseEvent) => {
  emit('click', event)
}

const handleTouchStart = (event: TouchEvent) => {
  isPressed.value = true
  emit('touchstart', event)
}

const handleTouchEnd = (event: TouchEvent) => {
  isPressed.value = false
  emit('touchend', event)
}
</script>

<style scoped>
.responsive-button {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.responsive-button.mobile-optimized {
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
}

.responsive-button.touch-optimized {
  cursor: pointer;
}

.responsive-button.touch-optimized:active {
  transform: scale(0.98);
}

/* 移动端优化 */
@media (max-width: 768px) {
  .responsive-button {
    font-size: 16px;
    letter-spacing: 0.025em;
  }
}

@media (max-width: 480px) {
  .responsive-button {
    font-size: 15px;
    border-radius: 10px;
  }
}

/* 确保按钮在各种状态下都有足够的对比度 */
:deep(.n-button__content) {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* 图标优化 */
:deep(.n-icon) {
  font-size: 18px;
}

@media (max-width: 768px) {
  :deep(.n-icon) {
    font-size: 20px;
  }
}
</style>
