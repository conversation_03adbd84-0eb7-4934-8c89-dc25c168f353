<template>
  <n-input
    v-bind="$attrs"
    :class="[
      'responsive-input',
      {
        'mobile-optimized': isMobile,
        'touch-optimized': isTouchDevice
      }
    ]"
    :style="inputStyle"
    :size="computedSize"
    @focus="handleFocus"
    @blur="handleBlur"
  >
    <template v-for="(_, slot) in $slots" #[slot]="slotProps">
      <slot :name="slot" v-bind="slotProps" />
    </template>
  </n-input>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { NInput } from 'naive-ui'
import { useResponsive, isTouchDevice } from '@/utils/responsive'

// Props
interface Props {
  // 是否全宽（移动端）
  fullWidthOnMobile?: boolean
  // 自定义最小高度
  minHeight?: string
}

const props = withDefaults(defineProps<Props>(), {
  fullWidthOnMobile: true
})

// Emits
const emit = defineEmits<{
  focus: [event: FocusEvent]
  blur: [event: FocusEvent]
}>()

// 响应式状态
const { isMobile, isXs } = useResponsive()
const isFocused = ref(false)

// 计算尺寸
const computedSize = computed(() => {
  if (isXs.value) return 'large'
  if (isMobile.value) return 'medium'
  return 'medium'
})

// 计算样式
const inputStyle = computed(() => {
  const style: Record<string, string> = {}
  
  // 基础最小高度
  if (isXs.value) {
    style.minHeight = props.minHeight || '52px'
    style.fontSize = '16px' // 防止iOS缩放
  } else if (isMobile.value) {
    style.minHeight = props.minHeight || '48px'
    style.fontSize = '16px' // 防止iOS缩放
  } else {
    style.minHeight = props.minHeight || '44px'
  }
  
  // 移动端全宽
  if (props.fullWidthOnMobile && isMobile.value) {
    style.width = '100%'
  }
  
  return style
})

// 事件处理
const handleFocus = (event: FocusEvent) => {
  isFocused.value = true
  emit('focus', event)
}

const handleBlur = (event: FocusEvent) => {
  isFocused.value = false
  emit('blur', event)
}
</script>

<style scoped>
.responsive-input {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.responsive-input.mobile-optimized {
  border-radius: 8px;
}

.responsive-input.touch-optimized {
  /* 优化触摸体验 */
  -webkit-tap-highlight-color: transparent;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .responsive-input {
    border-radius: 10px;
  }
  
  :deep(.n-input__input-el) {
    font-size: 16px !important; /* 防止iOS自动缩放 */
    line-height: 1.5;
  }
  
  :deep(.n-input__placeholder) {
    font-size: 16px !important;
  }
}

@media (max-width: 480px) {
  .responsive-input {
    border-radius: 12px;
  }
  
  :deep(.n-input__input-el) {
    font-size: 16px !important;
    padding: 14px 16px;
  }
}

/* 焦点状态优化 */
:deep(.n-input--focus) {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 图标优化 */
:deep(.n-input__prefix),
:deep(.n-input__suffix) {
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.n-input__prefix .n-icon),
:deep(.n-input__suffix .n-icon) {
  font-size: 18px;
}

@media (max-width: 768px) {
  :deep(.n-input__prefix .n-icon),
  :deep(.n-input__suffix .n-icon) {
    font-size: 20px;
  }
}

/* 清除按钮优化 */
:deep(.n-input__clear) {
  min-width: 44px;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 密码显示按钮优化 */
:deep(.n-input__password-toggle) {
  min-width: 44px;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
