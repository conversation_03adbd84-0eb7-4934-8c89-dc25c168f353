<template>
  <div class="search-input">
    <n-input
      v-model:value="searchValue"
      :placeholder="placeholder"
      :size="size"
      clearable
      @input="handleInput"
      @clear="handleClear"
    >
      <template #prefix>
        <n-icon :component="SearchOutline" />
      </template>
    </n-input>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { NInput, NIcon } from 'naive-ui'
import { SearchOutline } from '@vicons/ionicons5'

// Props定义
const props = defineProps({
  // 搜索值
  modelValue: {
    type: String,
    default: ''
  },
  // 占位符
  placeholder: {
    type: String,
    default: '请输入搜索关键词...'
  },
  // 尺寸
  size: {
    type: String as () => 'small' | 'medium' | 'large',
    default: 'medium'
  },
  // 防抖延迟（毫秒）
  debounce: {
    type: Number,
    default: 300
  }
})

// Emits定义
const emit = defineEmits<{
  'update:modelValue': [value: string]
  'search': [value: string]
}>()

// 响应式数据
const searchValue = ref(props.modelValue)

// 防抖定时器
let debounceTimer: number | null = null

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  searchValue.value = newValue
})

// 处理输入
const handleInput = (value: string) => {
  emit('update:modelValue', value)
  
  // 清除之前的定时器
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }
  
  // 设置新的防抖定时器
  debounceTimer = setTimeout(() => {
    emit('search', value)
  }, props.debounce)
}

// 处理清除
const handleClear = () => {
  emit('update:modelValue', '')
  emit('search', '')
  
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }
}
</script>

<style scoped>
.search-input {
  max-width: 400px;
}

:deep(.n-input) {
  border-radius: 8px;
}

:deep(.n-input__input-el) {
  font-size: 14px;
}

:deep(.n-input__prefix) {
  color: #9ca3af;
}
</style>
