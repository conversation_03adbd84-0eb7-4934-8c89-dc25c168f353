<template>
  <n-card
    class="stats-card"
    :class="[`stats-card--${color}`, { 'stats-card--loading': loading }]"
    hoverable
  >
    <n-spin :show="loading" size="small">
      <div class="stats-content">
        <div class="stats-icon">
          <n-icon :component="icon" size="24" />
        </div>
        <div class="stats-info">
          <div class="stats-value">{{ formattedValue }}</div>
          <div class="stats-label">{{ label }}</div>
        </div>
      </div>

      <!-- 趋势指示器 -->
      <div v-if="trend" class="stats-trend" :class="[`trend--${trend.type}`]">
        <n-icon
          :component="trend.type === 'up' ? TrendingUpOutline : TrendingDownOutline"
          size="16"
        />
        <span>{{ trend.value }}</span>
      </div>
    </n-spin>
  </n-card>
</template>

<script setup lang="ts">
import { computed, type Component } from 'vue'
import { NCard, NIcon, NSpin } from 'naive-ui'
import { TrendingUpOutline, TrendingDownOutline } from '@vicons/ionicons5'

// Props定义
const props = defineProps({
  // 数值
  value: {
    type: [Number, String],
    required: true
  },
  // 标签
  label: {
    type: String,
    required: true
  },
  // 图标
  icon: {
    type: Object as () => Component,
    required: true
  },
  // 颜色主题
  color: {
    type: String as () => 'primary' | 'success' | 'warning' | 'error' | 'info',
    default: 'primary'
  },
  // 格式化类型
  format: {
    type: String as () => 'number' | 'currency' | 'percentage',
    default: 'number'
  },
  // 趋势数据
  trend: {
    type: Object as () => {
      type: 'up' | 'down'
      value: string
    },
    default: undefined
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  }
})

// 计算属性 - 格式化数值
const formattedValue = computed(() => {
  const val = typeof props.value === 'string' ? parseFloat(props.value) : props.value
  
  if (isNaN(val)) {
    return props.value
  }
  
  switch (props.format) {
    case 'currency':
      return new Intl.NumberFormat('zh-CN', {
        style: 'currency',
        currency: 'CNY'
      }).format(val)
    
    case 'percentage':
      return `${val}%`
    
    case 'number':
    default:
      return new Intl.NumberFormat('zh-CN').format(val)
  }
})
</script>

<style scoped>
.stats-card {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stats-card--loading {
  opacity: 0.8;
}

.stats-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stats-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.stats-info {
  flex: 1;
}

.stats-value {
  font-size: 24px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.stats-trend {
  position: absolute;
  top: 12px;
  right: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 6px;
}

/* 颜色主题 */
.stats-card--primary .stats-icon {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.stats-card--primary .stats-value {
  color: #3b82f6;
}

.stats-card--success .stats-icon {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.stats-card--success .stats-value {
  color: #10b981;
}

.stats-card--warning .stats-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

.stats-card--warning .stats-value {
  color: #f59e0b;
}

.stats-card--error .stats-icon {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.stats-card--error .stats-value {
  color: #ef4444;
}

.stats-card--info .stats-icon {
  background: linear-gradient(135deg, #06b6d4, #0891b2);
  color: white;
}

.stats-card--info .stats-value {
  color: #06b6d4;
}

/* 趋势样式 */
.trend--up {
  background: #dcfce7;
  color: #16a34a;
}

.trend--down {
  background: #fee2e2;
  color: #dc2626;
}

/* 响应式 */
@media (max-width: 768px) {
  .stats-content {
    gap: 12px;
  }
  
  .stats-icon {
    width: 40px;
    height: 40px;
  }
  
  .stats-value {
    font-size: 20px;
  }
  
  .stats-label {
    font-size: 13px;
  }
}
</style>
