<template>
  <n-layout class="app-layout" has-sider>
    <!-- 移动端遮罩层 -->
    <div
      v-if="isMobile && !isCollapsed"
      class="mobile-overlay"
      @click="handleMobileOverlayClick"
      @touchstart="handleMobileOverlayClick"
    ></div>

    <!-- 左侧通顶侧边栏 -->
    <n-layout-sider
      bordered
      collapse-mode="width"
      :collapsed-width="isMobile ? 0 : 64"
      :width="240"
      :collapsed="isCollapsed"
      :show-trigger="false"
      class="app-sider"
      :class="{ 'mobile-sider': isMobile }"
      @mouseenter="handleSiderMouseEnter"
      @mouseleave="handleSiderMouseLeave"
      @touchstart="handleSiderTouchStart"
    >
      <!-- 侧边栏顶部标题区域 -->
      <div class="sider-header">
        <div class="sider-title" :class="{ collapsed: isCollapsed }">
          <span class="title-icon">⚙️</span>
          <span v-show="!isCollapsed" class="title-text">管理系统</span>
        </div>
        <!-- 移动端关闭按钮 -->
        <button
          v-if="isMobile && !isCollapsed"
          class="mobile-close-btn"
          @click="handleMobileClose"
          @touchstart="handleMobileClose"
        >
          ✕
        </button>
      </div>

      <!-- 侧边栏菜单 -->
      <n-menu
        :collapsed-width="isMobile ? 0 : 64"
        :collapsed-icon-size="22"
        :options="menuOptions"
        :value="activeMenuKey"
        :collapsed="isCollapsed"
        @update:value="handleMenuSelect"
        class="sider-menu"
      />
    </n-layout-sider>

    <!-- 右侧内容区域 -->
    <n-layout class="right-layout">
      <!-- 顶部导航 -->
      <n-layout-header class="app-header" bordered>
        <div class="header-content">
          <div class="header-left">
            <!-- 移动端菜单按钮 -->
            <button
              v-if="isMobile"
              class="mobile-menu-btn"
              @click="handleMobileMenuToggle"
              @touchstart="handleMobileMenuToggle"
            >
              <span class="menu-icon">☰</span>
            </button>
          </div>

          <div class="header-right">
            <div class="user-info">
              <n-icon :component="PersonOutline" size="18" />
              <span>{{ currentUser?.username || 'admin' }}</span>
            </div>

            <n-button
              type="default"
              size="small"
              @click="handleLogout"
              :loading="isLoggingOut"
            >
              <template #icon>
                <n-icon :component="LogOutOutline" />
              </template>
              退出登录
            </n-button>
          </div>
        </div>
      </n-layout-header>

      <!-- 主内容区域 -->
      <n-layout-content class="app-content">
        <div class="content-wrapper">
          <router-view v-slot="{ Component }">
            <keep-alive>
              <component :is="Component" />
            </keep-alive>
          </router-view>
        </div>
      </n-layout-content>
    </n-layout>
  </n-layout>
</template>

<script setup lang="ts">
import { computed, h, ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  NLayout,
  NLayoutHeader,
  NLayoutSider,
  NLayoutContent,
  NMenu,
  NButton,
  NIcon
} from 'naive-ui'
import {
  PersonOutline,
  LogOutOutline,
  PeopleOutline,
  BarChartOutline,
  CardOutline
} from '@vicons/ionicons5'
import { useAuthStore } from '@/stores'
import { useResponsive } from '@/utils/responsive'

// 路由
const router = useRouter()

// 状态管理
const authStore = useAuthStore()

// 响应式Hook
const { isMobile } = useResponsive()

// 计算属性
const currentUser = computed(() => authStore.currentUser)
const isLoggingOut = ref(false)

// 侧边栏收缩状态 - 移动端默认收缩，桌面端根据需要
const isCollapsed = ref(true)

// 当前激活的菜单项
const activeMenuKey = computed(() => {
  const route = router.currentRoute.value
  return route.name as string
})

// 菜单配置 - 修复图标渲染问题
const menuOptions = [
  {
    label: '👥 用户管理',
    key: 'users',
    icon: () => h(NIcon, { component: PeopleOutline })
  },
  {
    label: '📊 用量统计',
    key: 'usage',
    icon: () => h(NIcon, { component: BarChartOutline })
  },
  {
    label: '💳 卡密管理',
    key: 'cards',
    icon: () => h(NIcon, { component: CardOutline })
  }
]

// 处理菜单选择
const handleMenuSelect = (key: string) => {
  router.push({ name: key })
}

// 处理侧边栏鼠标事件（仅桌面端）
const handleSiderMouseEnter = () => {
  if (!isMobile.value) {
    isCollapsed.value = false
  }
}

const handleSiderMouseLeave = () => {
  if (!isMobile.value) {
    isCollapsed.value = true
  }
}

// 移动端触摸事件处理
const handleSiderTouchStart = () => {
  if (isMobile.value && isCollapsed.value) {
    isCollapsed.value = false
  }
}

// 移动端遮罩层点击
const handleMobileOverlayClick = () => {
  if (isMobile.value) {
    isCollapsed.value = true
  }
}

// 移动端关闭按钮
const handleMobileClose = () => {
  if (isMobile.value) {
    isCollapsed.value = true
  }
}

// 移动端菜单切换
const handleMobileMenuToggle = () => {
  if (isMobile.value) {
    isCollapsed.value = !isCollapsed.value
  }
}

// 处理登出
const handleLogout = async () => {
  isLoggingOut.value = true

  try {
    authStore.logout()
    router.push('/login')
  } catch (error) {
    console.error('退出登录失败:', error)
  } finally {
    isLoggingOut.value = false
  }
}
</script>



<style scoped>
.app-layout {
  height: 100vh;
  display: flex;
}

/* 右侧布局容器 */
.right-layout {
  flex: 1;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  height: 64px;
  display: flex;
  align-items: center;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  z-index: 100;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 24px;
}

.header-left {
  display: flex;
  align-items: center;
}

/* 移除旧的标题样式，现在标题在侧边栏中 */

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f8fafc;
  border-radius: 6px;
  font-size: 14px;
  color: #4b5563;
  font-weight: 500;
}

/* 侧边栏样式 */
.app-sider {
  height: 100vh !important;
  background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
  box-shadow: 2px 0 12px rgba(0, 0, 0, 0.08);
  border-right: 1px solid #e2e8f0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 10;
  display: flex;
  flex-direction: column;
}

.app-sider:hover {
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.12);
}

/* 侧边栏头部 */
.sider-header {
  height: 64px;
  display: flex;
  align-items: center;
  padding: 0 16px;
  border-bottom: 1px solid #e2e8f0;
  flex-shrink: 0;
}

.sider-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
  transition: all 0.3s ease;
}

.sider-title.collapsed {
  justify-content: center;
}

.title-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.title-text {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  white-space: nowrap;
}

/* 侧边栏菜单 */
.sider-menu {
  flex: 1;
  padding-top: 16px;
  background: transparent !important;
}

/* 菜单容器背景 */
:deep(.n-menu) {
  background: transparent !important;
  color: #374151;
}

/* 确保菜单项背景透明 - 强制覆盖所有可能的背景 */
:deep(.n-menu .n-menu-item:not(.n-menu-item--selected)) {
  background: transparent !important;
  background-color: transparent !important;
  color: #374151;
}

:deep(.n-menu .n-menu-item:not(.n-menu-item--selected) .n-menu-item-content) {
  background: transparent !important;
  background-color: transparent !important;
}

:deep(.n-menu .n-menu-item:not(.n-menu-item--selected) .n-icon) {
  color: #6b7280;
}

:deep(.n-menu .n-menu-item:not(.n-menu-item--selected) .n-menu-item-content-header) {
  color: #374151;
}

/* 强制覆盖Naive UI的默认背景样式 */
:deep(.n-menu-item:not(.n-menu-item--selected):not(:hover)) {
  background: transparent !important;
  background-color: transparent !important;
}

:deep(.n-menu--collapsed .n-menu-item:not(.n-menu-item--selected):not(:hover)) {
  background: transparent !important;
  background-color: transparent !important;
}

.app-content {
  background: #f8fafc;
  overflow: auto;
  flex: 1;
  height: calc(100vh - 64px);
}

.content-wrapper {
  padding: 12px;
  min-height: 100%;
  max-width: none;
  width: 100%;
}

/* 菜单样式 - 现代化设计 */
:deep(.n-menu-item) {
  margin: 6px 12px;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  background: transparent !important;
  background-color: transparent !important;
}

/* 强制覆盖所有可能的菜单项背景 */
:deep(.n-menu-item),
:deep(.n-menu-item .n-menu-item-content),
:deep(.n-menu-item .n-menu-item-content-header),
:deep(.n-menu-item .n-menu-item-content__arrow),
:deep(.n-menu-item .n-menu-item-content__icon) {
  background: transparent !important;
  background-color: transparent !important;
}

/* 收缩状态下的菜单项样式 */
:deep(.n-menu--collapsed .n-menu-item) {
  margin: 6px 8px;
  border-radius: 10px;
  justify-content: center;
  background: transparent !important;
  background-color: transparent !important;
}

/* 收缩状态下强制透明背景 */
:deep(.n-menu--collapsed .n-menu-item:not(.n-menu-item--selected)) {
  background: transparent !important;
  background-color: transparent !important;
}

:deep(.n-menu--collapsed .n-menu-item:not(.n-menu-item--selected) *) {
  background: transparent !important;
  background-color: transparent !important;
}

:deep(.n-menu-item::before) {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

/* 选中状态 - 展开时（保持背景色） */
:deep(.n-menu:not(.n-menu--collapsed) .n-menu-item--selected) {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
  color: white !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transform: translateX(2px);
}

/* 选中状态 - 收缩时（仅图标变色） */
:deep(.n-menu--collapsed .n-menu-item--selected) {
  background: transparent !important;
  color: #3b82f6 !important;
  transform: scale(1.05);
}

:deep(.n-menu-item--selected::before) {
  opacity: 1;
}

/* 展开状态选中项文字和图标 */
:deep(.n-menu:not(.n-menu--collapsed) .n-menu-item--selected .n-menu-item-content-header) {
  color: white !important;
  font-weight: 600;
}

:deep(.n-menu:not(.n-menu--collapsed) .n-menu-item--selected .n-icon) {
  color: white !important;
}

/* 收缩状态选中项图标（蓝色） */
:deep(.n-menu--collapsed .n-menu-item--selected .n-icon) {
  color: #3b82f6 !important;
  font-weight: 600;
  filter: drop-shadow(0 0 4px rgba(59, 130, 246, 0.3));
}

/* 悬停效果 - 展开时 */
:deep(.n-menu:not(.n-menu--collapsed) .n-menu-item:hover:not(.n-menu-item--selected)) {
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0) !important;
  transform: translateX(1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 悬停效果 - 收缩时（更微妙的效果） */
:deep(.n-menu--collapsed .n-menu-item:hover:not(.n-menu-item--selected)) {
  background: rgba(59, 130, 246, 0.08) !important;
  transform: scale(1.02);
  border-radius: 10px;
}

:deep(.n-menu--collapsed .n-menu-item:hover:not(.n-menu-item--selected) .n-icon) {
  color: #3b82f6 !important;
  transform: scale(1.1);
}

/* 选中项悬停效果 - 展开状态 */
:deep(.n-menu:not(.n-menu--collapsed) .n-menu-item--selected:hover) {
  background: linear-gradient(135deg, #2563eb, #1e40af) !important;
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
  transform: translateX(3px);
}

/* 选中项悬停效果 - 收缩状态（图标变色增强） */
:deep(.n-menu--collapsed .n-menu-item--selected:hover) {
  background: rgba(59, 130, 246, 0.12) !important;
  transform: scale(1.08);
}

:deep(.n-menu--collapsed .n-menu-item--selected:hover .n-icon) {
  color: #2563eb !important;
  transform: scale(1.15);
  filter: drop-shadow(0 0 6px rgba(59, 130, 246, 0.5));
}

/* 菜单项内容 */
:deep(.n-menu-item-content) {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

:deep(.n-menu--collapsed .n-menu-item-content) {
  padding: 12px;
  justify-content: center;
}

:deep(.n-menu-item-content-header) {
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0.025em;
}

/* 图标样式优化 */
:deep(.n-menu-item .n-icon) {
  font-size: 18px;
  transition: all 0.3s ease;
}

:deep(.n-menu--collapsed .n-menu-item .n-icon) {
  font-size: 20px;
}

/* 最强力的背景覆盖 - 使用最高特异性 */
:deep(.app-sider .n-menu.n-menu--collapsed .n-menu-item:not(.n-menu-item--selected)) {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
}

:deep(.app-sider .n-menu.n-menu--collapsed .n-menu-item:not(.n-menu-item--selected):not(:hover)) {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
}

/* 覆盖可能的CSS变量 */
:deep(.app-sider .n-menu--collapsed) {
  --n-item-color: transparent;
  --n-item-color-hover: rgba(59, 130, 246, 0.08);
  --n-item-color-active: transparent;
  --n-item-color-active-hover: rgba(59, 130, 246, 0.12);
}

/* 确保所有子元素也是透明的 */
:deep(.app-sider .n-menu--collapsed .n-menu-item:not(.n-menu-item--selected) > *) {
  background: transparent !important;
  background-color: transparent !important;
}

/* 移动端遮罩层 */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  transition: opacity 0.3s ease;
}

/* 移动端侧边栏 */
.mobile-sider {
  position: fixed !important;
  top: 0;
  left: 0;
  z-index: 1000;
  transform: translateX(-100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-sider:not(.n-layout-sider--collapsed) {
  transform: translateX(0);
}

/* 移动端菜单按钮 */
.mobile-menu-btn {
  background: none;
  border: none;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  /* 确保触摸目标足够大 */
  min-width: 44px;
  min-height: 44px;
}

.mobile-menu-btn:hover,
.mobile-menu-btn:active {
  background: rgba(0, 0, 0, 0.05);
}

.menu-icon {
  font-size: 18px;
  color: #666;
  font-weight: bold;
}

/* 移动端关闭按钮 */
.mobile-close-btn {
  position: absolute;
  top: 50%;
  right: 16px;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.1);
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
  /* 确保触摸目标足够大 */
  min-width: 44px;
  min-height: 44px;
}

.mobile-close-btn:hover,
.mobile-close-btn:active {
  background: rgba(0, 0, 0, 0.2);
  color: #333;
}

/* 响应式 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 16px;
  }

  .app-title {
    font-size: 18px;
  }

  .user-info span {
    display: none;
  }

  .content-wrapper {
    padding: 8px;
  }

  /* 移动端侧边栏优化 */
  .app-sider {
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.15);
  }

  /* 移动端菜单项触摸优化 */
  :deep(.n-menu-item-content) {
    padding: 16px 20px;
    min-height: 48px;
    display: flex;
    align-items: center;
  }

  /* 移动端图标和文字间距 */
  :deep(.n-menu-item .n-icon) {
    margin-right: 12px;
  }
}

/* 小屏幕设备优化 */
@media (max-width: 480px) {
  .header-content {
    padding: 0 12px;
  }

  .content-wrapper {
    padding: 8px;
  }

  .user-info {
    gap: 6px;
  }

  /* 更大的触摸目标 */
  :deep(.n-menu-item-content) {
    padding: 18px 20px;
    min-height: 52px;
  }

  .mobile-close-btn {
    min-width: 48px;
    min-height: 48px;
    right: 12px;
  }
}

/* 平板设备优化 */
@media (min-width: 769px) and (max-width: 1024px) {
  .content-wrapper {
    padding: 8px;
  }

  .header-content {
    padding: 0 24px;
  }
}

/* 大屏幕优化 */
@media (min-width: 1200px) {
  .content-wrapper {
    padding: 8px;
  }
}

/* 超大屏幕优化 */
@media (min-width: 1600px) {
  .content-wrapper {
    padding: 8px 8px;
  }
}
</style>
