import type { VipType } from '@/types'

// VIP类型配置
export const VIP_TYPES: VipType[] = [
  { 
    value: 'monthly', 
    label: '月卡', 
    description: '30天VIP权限', 
    durationDays: 30 
  },
  { 
    value: 'quarterly', 
    label: '季卡', 
    description: '90天VIP权限', 
    durationDays: 90 
  },
  { 
    value: 'yearly', 
    label: '年卡', 
    description: '365天VIP权限', 
    durationDays: 365 
  },
  { 
    value: 'lifetime', 
    label: '永久VIP', 
    description: '永久有效，无到期时间限制' 
  },
  { 
    value: 'custom', 
    label: '自定义', 
    description: '自定义时长和类型' 
  }
]

// VIP类型映射
export const VIP_TYPE_MAP = VIP_TYPES.reduce((map, type) => {
  map[type.value] = type
  return map
}, {} as Record<string, VipType>)

// 获取VIP类型标签
export const getVipTypeLabel = (type: string): string => {
  return VIP_TYPE_MAP[type]?.label || type
}

// 获取VIP类型描述
export const getVipTypeDescription = (type: string): string => {
  return VIP_TYPE_MAP[type]?.description || ''
}

// 计算VIP到期时间
export const calculateVipExpireTime = (type: string, baseTime: number = Date.now()): number => {
  const vipType = VIP_TYPE_MAP[type]
  if (!vipType?.durationDays) {
    return baseTime
  }
  
  return baseTime + vipType.durationDays * 24 * 60 * 60 * 1000
}

// 检查VIP是否过期
export const isVipExpired = (expireAt: number): boolean => {
  return expireAt <= Date.now()
}

// 格式化VIP状态显示
export const formatVipStatus = (vip?: { type: string; expireAt: number }): string => {
  if (!vip) {
    return '非VIP'
  }
  
  const expireDate = new Date(vip.expireAt).toLocaleDateString('zh-CN')
  const typeLabel = getVipTypeLabel(vip.type)
  const expired = isVipExpired(vip.expireAt)
  
  return `${typeLabel} - ${expireDate}${expired ? ' (已过期)' : ''}`
}

// VIP操作类型
export const VIP_ACTIONS = [
  { value: 'set', label: '设置VIP', description: '为用户设置新的VIP状态' },
  { value: 'extend', label: '延长时间', description: '延长现有VIP的有效期' },
  { value: 'remove', label: '移除VIP', description: '完全移除用户的VIP状态' }
] as const

export type VipActionType = typeof VIP_ACTIONS[number]['value']
