import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/users'
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('@/views/LoginView.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/',
      component: () => import('@/components/layout/AppLayout.vue'),
      meta: { requiresAuth: true },
      children: [
        {
          path: 'users',
          name: 'users',
          component: () => import('@/views/admin/UsersView.vue')
        },
        {
          path: 'usage',
          name: 'usage',
          component: () => import('@/views/admin/UsageView.vue')
        },
        {
          path: 'cards',
          name: 'cards',
          component: () => import('@/views/admin/CardsView.vue')
        }
      ]
    }
  ]
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // 确保认证状态是最新的
  authStore.checkAuth()

  console.log('路由守卫检查:', {
    to: to.path,
    isAuthenticated: authStore.isAuthenticated,
    requiresAuth: to.meta.requiresAuth
  })

  // 检查路由是否需要认证
  if (to.meta.requiresAuth !== false) {
    if (!authStore.isAuthenticated) {
      console.log('未认证，重定向到登录页')
      next('/login')
      return
    }
  }

  // 如果已登录且访问登录页，重定向到首页
  if (to.name === 'login' && authStore.isAuthenticated) {
    console.log('已登录，重定向到用户页')
    next('/users')
    return
  }

  next()
})

export default router
