/**
 * API模式管理服务
 * 负责管理用户选择的API模式（新版/兼容模式）
 */

export type APIMode = 'new' | 'legacy'

export interface APIModeConfig {
  mode: APIMode
  timestamp: number
  userSelected: boolean // 是否为用户主动选择
}

export class APIModeManager {
  private static readonly STORAGE_KEY = 'tts_api_mode_config'
  private static readonly CACHE_DURATION = 7 * 24 * 60 * 60 * 1000 // 7天缓存

  /**
   * 获取当前API模式配置
   */
  static getCurrentConfig(): APIModeConfig {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY)
      if (stored) {
        const config: APIModeConfig = JSON.parse(stored)
        
        // 检查缓存是否过期
        if (Date.now() - config.timestamp < this.CACHE_DURATION) {
          return config
        }
      }
    } catch (error) {
      console.warn('读取API模式配置失败:', error)
    }

    // 返回默认配置
    return {
      mode: 'new',
      timestamp: Date.now(),
      userSelected: false
    }
  }

  /**
   * 获取当前API模式
   */
  static getCurrentMode(): APIMode {
    return this.getCurrentConfig().mode
  }

  /**
   * 设置API模式
   */
  static setMode(mode: APIMode, userSelected: boolean = true): void {
    const config: APIModeConfig = {
      mode,
      timestamp: Date.now(),
      userSelected
    }

    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(config))
      console.log(`API模式已设置为: ${mode}${userSelected ? ' (用户选择)' : ' (自动检测)'}`)
    } catch (error) {
      console.error('保存API模式配置失败:', error)
    }
  }

  /**
   * 检查是否为用户主动选择的模式
   */
  static isUserSelected(): boolean {
    return this.getCurrentConfig().userSelected
  }

  /**
   * 重置为默认模式
   */
  static reset(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY)
      console.log('API模式配置已重置')
    } catch (error) {
      console.error('重置API模式配置失败:', error)
    }
  }

  /**
   * 获取模式的友好名称
   */
  static getModeName(mode: APIMode): string {
    const names = {
      new: '新版API',
      legacy: '兼容模式'
    }
    return names[mode]
  }

  /**
   * 获取模式的描述
   */
  static getModeDescription(mode: APIMode): string {
    const descriptions = {
      new: '使用最新的RESTful API接口，功能更完整，性能更好',
      legacy: '使用传统的授权码认证方式，兼容旧版系统'
    }
    return descriptions[mode]
  }

  /**
   * 检查模式是否有效
   */
  static isValidMode(mode: string): mode is APIMode {
    return mode === 'new' || mode === 'legacy'
  }
}
