/**
 * API路由器服务
 * 根据当前API模式智能路由请求到正确的端点
 */

import { APIModeManager, type APIMode } from './apiModeManager'
import { API_ENDPOINT_MAP, API_BASE_URL_MAP } from '@/api/config'

export type APIFeature = keyof typeof API_ENDPOINT_MAP.new

export class APIRouter {
  /**
   * 根据当前API模式获取端点路径
   */
  static getEndpoint(feature: APIFeature): string {
    const mode = APIModeManager.getCurrentMode()
    const endpoint = API_ENDPOINT_MAP[mode][feature]

    if (!endpoint) {
      console.warn(`API端点 ${feature} 在 ${mode} 模式下不可用，使用默认端点`)
      return API_ENDPOINT_MAP.new[feature] || ''
    }

    return endpoint
  }

  /**
   * 根据当前API模式获取完整URL
   */
  static getFullURL(feature: APIFeature): string {
    const mode = APIModeManager.getCurrentMode()
    const baseURL = API_BASE_URL_MAP[mode]
    const endpoint = this.getEndpoint(feature)

    return `${baseURL}${endpoint}`
  }

  /**
   * 根据当前API模式获取基础URL
   */
  static getBaseURL(): string {
    const mode = APIModeManager.getCurrentMode()
    return API_BASE_URL_MAP[mode]
  }

  /**
   * 根据指定模式获取端点
   */
  static getEndpointByMode(feature: APIFeature, mode: APIMode): string {
    const endpoint = API_ENDPOINT_MAP[mode][feature]
    
    if (!endpoint) {
      console.warn(`API端点 ${feature} 在 ${mode} 模式下不可用`)
      return ''
    }
    
    return endpoint
  }

  /**
   * 检查功能在当前模式下是否可用
   */
  static isFeatureAvailable(feature: APIFeature): boolean {
    const mode = APIModeManager.getCurrentMode()
    const endpoint = API_ENDPOINT_MAP[mode][feature]
    return Boolean(endpoint)
  }

  /**
   * 检查功能在指定模式下是否可用
   */
  static isFeatureAvailableInMode(feature: APIFeature, mode: APIMode): boolean {
    const endpoint = API_ENDPOINT_MAP[mode][feature]
    return Boolean(endpoint)
  }

  /**
   * 获取当前模式下可用的功能列表
   */
  static getAvailableFeatures(): APIFeature[] {
    const mode = APIModeManager.getCurrentMode()
    const features: APIFeature[] = []
    
    for (const feature in API_ENDPOINT_MAP[mode]) {
      if (API_ENDPOINT_MAP[mode][feature as APIFeature]) {
        features.push(feature as APIFeature)
      }
    }
    
    return features
  }

  /**
   * 比较两种模式下的功能差异
   */
  static compareModesFeatures(): {
    newOnly: APIFeature[]
    legacyOnly: APIFeature[]
    common: APIFeature[]
  } {
    const newFeatures = this.getAvailableFeaturesInMode('new')
    const legacyFeatures = this.getAvailableFeaturesInMode('legacy')
    
    const newOnly = newFeatures.filter(f => !legacyFeatures.includes(f))
    const legacyOnly = legacyFeatures.filter(f => !newFeatures.includes(f))
    const common = newFeatures.filter(f => legacyFeatures.includes(f))
    
    return { newOnly, legacyOnly, common }
  }

  /**
   * 获取指定模式下可用的功能列表
   */
  private static getAvailableFeaturesInMode(mode: APIMode): APIFeature[] {
    const features: APIFeature[] = []
    
    for (const feature in API_ENDPOINT_MAP[mode]) {
      if (API_ENDPOINT_MAP[mode][feature as APIFeature]) {
        features.push(feature as APIFeature)
      }
    }
    
    return features
  }

  /**
   * 记录API调用信息（用于调试）
   */
  static logAPICall(feature: APIFeature, endpoint: string): void {
    const mode = APIModeManager.getCurrentMode()
    const baseURL = API_BASE_URL_MAP[mode]
    const fullURL = `${baseURL}${endpoint}`
    console.log(`[API路由] ${APIModeManager.getModeName(mode)} - ${feature}: ${fullURL}`)
  }
}
