import { defineStore } from 'pinia'
import { ref, computed, nextTick } from 'vue'
import { AuthService } from '@/api'
import type { APIMode } from '@/services/apiModeManager'
import type { AuthRequest } from '@/types'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string | null>(null)
  const username = ref<string>('')
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const isAuthenticated = computed(() => {
    // 确保token状态变化时重新计算
    if (token.value && AuthService.isAuthenticated()) {
      return true
    }
    return false
  })

  const currentUser = computed(() => {
    if (!isAuthenticated.value) return null
    return {
      username: username.value || 'admin',
      token: token.value
    }
  })

  // 动作
  const login = async (authData: AuthRequest, apiMode?: APIMode) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await AuthService.login(authData, apiMode)

      // 处理新旧API的不同响应格式
      if ((response.success && response.token) || response.token) {
        // 立即更新store状态
        token.value = response.token!
        username.value = authData.username || response.user?.username || 'admin'

        // 确保状态同步
        await nextTick()

        return true
      } else {
        error.value = response.message || '登录失败'
        return false
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '登录失败'
      return false
    } finally {
      isLoading.value = false
    }
  }

  const logout = () => {
    AuthService.logout()
    token.value = null
    username.value = ''
    error.value = null
  }

  const checkAuth = () => {
    const user = AuthService.getCurrentUser()
    console.log('检查认证状态:', { user, isAuth: AuthService.isAuthenticated() })

    if (user) {
      token.value = user.token
      username.value = user.username
    } else {
      token.value = null
      username.value = ''
    }

    console.log('认证状态更新后:', {
      token: token.value ? '已设置' : '未设置',
      username: username.value,
      isAuthenticated: isAuthenticated.value
    })
  }

  const clearError = () => {
    error.value = null
  }

  // 初始化时检查认证状态
  checkAuth()

  return {
    // 状态
    token,
    username,
    isLoading,
    error,
    // 计算属性
    isAuthenticated,
    currentUser,
    // 动作
    login,
    logout,
    checkAuth,
    clearError
  }
})
