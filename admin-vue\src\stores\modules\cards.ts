import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { CardService } from '@/api'
import type { Card, SortState, CardType } from '@/types'

export const useCardsStore = defineStore('cards', () => {
  // 状态
  const cards = ref<Card[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  
  // 生成相关状态
  const isGenerating = ref(false)
  const generatedCards = ref<string[]>([])
  const generateProgress = ref(0)
  const generateTotal = ref(0)
  
  // 搜索和筛选状态
  const searchTerm = ref('')
  const selectedCards = ref<string[]>([])
  
  // 排序状态
  const sortState = ref<SortState>({
    field: 'createdAt',
    direction: 'desc'
  })

  // 计算属性 - 过滤和排序后的卡密数据
  const filteredCards = computed(() => {
    let result = [...cards.value]

    // 搜索过滤
    if (searchTerm.value) {
      const term = searchTerm.value.toLowerCase().trim()
      result = result.filter(card => {
        try {
          // 安全的字符串转换和检查，兼容新旧数据格式
          const code = String(card.code || card.c || '').toLowerCase()
          const type = String(card.type || card.package_type || card.t || '').toLowerCase()
          const status = String(card.status || card.s || '').toLowerCase()

          // 处理使用者字段的多种可能格式
          const usedBy = String(
            card.usedBy ||
            card.used_by ||
            card.user ||
            card.u ||
            ''
          ).toLowerCase()

          // 获取卡密类型的友好名称
          const typeName = CardService.getCardTypeName(
            card.type || card.package_type || card.t || ''
          ).toLowerCase()

          // 状态的友好名称
          const statusName = (card.status === 'unused' || card.s === 'unused') ? '未使用' :
                           (card.status === 'used' || card.s === 'used') ? '已使用' : ''

          return code.includes(term) ||
                 type.includes(term) ||
                 typeName.includes(term) ||
                 status.includes(term) ||
                 statusName.includes(term) ||
                 usedBy.includes(term)
        } catch (error) {
          console.warn('卡密过滤时发生错误:', error, '卡密数据:', card)
          return false // 如果处理出错，不包含在结果中
        }
      })
    }

    // 排序 - 兼容新旧数据格式
    if (sortState.value.field) {
      result.sort((a, b) => {
        const field = sortState.value.field!
        let valueA: any
        let valueB: any

        try {
          // 根据字段名获取对应的值，兼容新旧格式
          switch (field) {
            case 'code':
              valueA = String(a.code || a.c || '')
              valueB = String(b.code || b.c || '')
              break
            case 'type':
              valueA = String(a.type || a.package_type || a.t || '')
              valueB = String(b.type || b.package_type || b.t || '')
              break
            case 'status':
              valueA = String(a.status || a.s || '')
              valueB = String(b.status || b.s || '')
              break
            case 'usedBy':
              valueA = String(a.usedBy || a.used_by || a.user || a.u || '')
              valueB = String(b.usedBy || b.used_by || b.user || b.u || '')
              break
            case 'createdAt':
              valueA = a.createdAt || a.created_at || 0
              valueB = b.createdAt || b.created_at || 0
              break
            case 'activatedAt':
              valueA = a.activatedAt || a.activated_at || a.a || 0
              valueB = b.activatedAt || b.activated_at || b.a || 0
              break
            default:
              valueA = (a as any)[field] || ''
              valueB = (b as any)[field] || ''
          }

          // 确保值不为null或undefined
          valueA = valueA ?? ''
          valueB = valueB ?? ''

          if (valueA === valueB) return 0

          const modifier = sortState.value.direction === 'asc' ? 1 : -1
          return valueA > valueB ? modifier : -modifier
        } catch (error) {
          console.warn('卡密排序时发生错误:', error, '字段:', field)
          return 0 // 如果排序出错，保持原顺序
        }
      })
    }

    return result
  })

  // 卡密类型状态
  const cardTypes = ref<CardType[]>(CardService.CARD_TYPES)
  const isLoadingTypes = ref(false)

  // 动作 - 加载套餐类型
  const loadCardTypes = async () => {
    if (isLoadingTypes.value) return

    isLoadingTypes.value = true
    try {
      const types = await CardService.getPackageTypes()
      cardTypes.value = types as CardType[]
    } catch (err) {
      console.warn('加载套餐类型失败，使用默认配置:', err)
      cardTypes.value = CardService.CARD_TYPES
    } finally {
      isLoadingTypes.value = false
    }
  }

  // 计算属性 - 是否全选
  const isAllSelected = computed(() => {
    return filteredCards.value.length > 0 && 
           selectedCards.value.length === filteredCards.value.length
  })

  // 动作 - 加载卡密列表
  const loadCards = async () => {
    isLoading.value = true
    error.value = null

    try {
      const data = await CardService.getCards()
      cards.value = data
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载卡密失败'
    } finally {
      isLoading.value = false
    }
  }

  // 动作 - 生成卡密预览
  const generatePreview = async (type: string): Promise<string> => {
    try {
      return await CardService.generatePreview(type)
    } catch (err) {
      error.value = err instanceof Error ? err.message : '生成预览失败'
      throw err
    }
  }

  // 动作 - 批量生成卡密 (使用新API的批量功能)
  const generateCards = async (type: string, count: number, customCode?: string) => {
    isGenerating.value = true
    generateProgress.value = 0
    generateTotal.value = count
    generatedCards.value = []
    error.value = null

    try {
      if (count === 1 && customCode) {
        // 单个自定义卡密生成
        const code = await CardService.generateCard({
          packageType: type,
          type,
          customCode
        })
        generatedCards.value = [code]
      } else {
        // 批量生成
        const codes = await CardService.generateCards(type, count)
        generatedCards.value = codes
      }

      generateProgress.value = generatedCards.value.length

      // 重新加载卡密列表
      await loadCards()
    } catch (err) {
      error.value = err instanceof Error ? err.message : '生成卡密失败'
      throw err
    } finally {
      isGenerating.value = false
    }
  }

  // 动作 - 编辑卡密
  const editCard = async (oldCode: string, newCode: string, type: string) => {
    try {
      await CardService.editCard({ oldCode, newCode, type })
      
      // 更新本地数据
      const cardIndex = cards.value.findIndex(card => card.code === oldCode)
      if (cardIndex !== -1) {
        cards.value[cardIndex].code = newCode
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '编辑卡密失败'
      throw err
    }
  }

  // 动作 - 删除卡密
  const deleteCard = async (code: string) => {
    try {
      await CardService.deleteCard(code)
      
      // 从本地数据中移除
      const cardIndex = cards.value.findIndex(card => card.code === code)
      if (cardIndex !== -1) {
        cards.value.splice(cardIndex, 1)
      }
      
      // 从选中列表中移除
      const selectedIndex = selectedCards.value.indexOf(code)
      if (selectedIndex !== -1) {
        selectedCards.value.splice(selectedIndex, 1)
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除卡密失败'
      throw err
    }
  }

  // 动作 - 批量删除卡密
  const deleteSelectedCards = async () => {
    const codes = [...selectedCards.value]
    
    for (const code of codes) {
      try {
        await deleteCard(code)
      } catch (err) {
        console.error(`删除卡密 ${code} 失败:`, err)
      }
    }
    
    selectedCards.value = []
  }

  // 动作 - 设置排序
  const setSort = (field: string) => {
    if (sortState.value.field === field) {
      sortState.value.direction = sortState.value.direction === 'asc' ? 'desc' : 'asc'
    } else {
      sortState.value.field = field
      sortState.value.direction = 'asc'
    }
  }

  // 动作 - 设置搜索
  const setSearch = (term: string) => {
    searchTerm.value = term
  }

  // 动作 - 切换卡密选中状态
  const toggleCardSelection = (code: string) => {
    const index = selectedCards.value.indexOf(code)
    if (index === -1) {
      selectedCards.value.push(code)
    } else {
      selectedCards.value.splice(index, 1)
    }
  }

  // 动作 - 全选/取消全选
  const toggleSelectAll = () => {
    if (isAllSelected.value) {
      selectedCards.value = []
    } else {
      selectedCards.value = filteredCards.value.map(card => card.code)
    }
  }

  // 动作 - 清空生成结果
  const clearGeneratedCards = () => {
    generatedCards.value = []
    generateProgress.value = 0
    generateTotal.value = 0
  }

  // 动作 - 清除错误
  const clearError = () => {
    error.value = null
  }

  return {
    // 状态
    cards,
    isLoading,
    error,
    isGenerating,
    generatedCards,
    generateProgress,
    generateTotal,
    searchTerm,
    selectedCards,
    sortState,
    // 计算属性
    filteredCards,
    isAllSelected,
    // 状态
    cardTypes,
    isLoadingTypes,
    // 动作
    loadCards,
    loadCardTypes,
    generatePreview,
    generateCards,
    editCard,
    deleteCard,
    deleteSelectedCards,
    setSort,
    setSearch,
    toggleCardSelection,
    toggleSelectAll,
    clearGeneratedCards,
    clearError
  }
})
