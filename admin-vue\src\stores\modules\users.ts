import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { UserService } from '@/api'
import type { User, UsageData, SortState, FilterState, StatsData, PaginationParams } from '@/types'

export const useUsersStore = defineStore('users', () => {
  // 用户管理状态
  const users = ref<User[]>([])
  const isLoadingUsers = ref(false)
  const usersError = ref<string | null>(null)

  // 用量统计状态
  const usageData = ref<UsageData[]>([])
  const isLoadingUsage = ref(false)
  const usageError = ref<string | null>(null)
  const usageStats = ref<StatsData>({
    totalUsers: 0,
    totalCharsUsed: 0,
    monthlyCharsUsed: 0,
    vipUsersCount: 0
  })
  const usagePagination = ref({
    hasMore: false,
    nextCursor: undefined as string | undefined
  })

  // 全局统计状态
  const globalStats = ref<StatsData>({
    totalUsers: 0,
    totalCharsUsed: 0,
    monthlyCharsUsed: 0,
    vipUsersCount: 0
  })
  const isLoadingGlobalStats = ref(false)
  const globalStatsError = ref<string | null>(null)

  // 搜索和筛选状态
  const userFilter = ref<FilterState>({
    searchTerm: '',
    activeFilter: 'all'
  })

  const usageFilter = ref<FilterState>({
    searchTerm: '',
    activeFilter: 'active'
  })

  // 排序状态
  const userSort = ref<SortState>({
    field: 'createAt',
    direction: 'desc'
  })

  const usageSort = ref<SortState>({
    field: 'usage.totalChars',
    direction: 'desc'
  })

  // 计算属性 - 过滤和排序后的用户数据
  const filteredUsers = computed(() => {
    let result = [...users.value]

    // 搜索过滤
    if (userFilter.value.searchTerm) {
      const term = userFilter.value.searchTerm.toLowerCase()
      result = result.filter(user => 
        user.username.toLowerCase().includes(term) ||
        (user.vip ? '有VIP' : '无VIP').includes(term)
      )
    }

    // 排序
    if (userSort.value.field) {
      result.sort((a, b) => {
        const field = userSort.value.field!
        let valueA: any = getNestedValue(a, field)
        let valueB: any = getNestedValue(b, field)

        // 特殊处理VIP字段
        if (field === 'vip.expireAt') {
          valueA = a.vip ? a.vip.expireAt : -1
          valueB = b.vip ? b.vip.expireAt : -1
        }

        if (valueA === valueB) return 0
        
        const modifier = userSort.value.direction === 'asc' ? 1 : -1
        return valueA > valueB ? modifier : -modifier
      })
    }

    return result
  })

  // 计算属性 - 过滤和排序后的用量数据
  const filteredUsageData = computed(() => {
    let result = [...usageData.value]

    // 搜索过滤
    if (usageFilter.value.searchTerm) {
      const term = usageFilter.value.searchTerm.toLowerCase()
      result = result.filter(user => 
        user.username.toLowerCase().includes(term)
      )
    }

    // 活跃状态过滤
    if (usageFilter.value.activeFilter === 'active') {
      result = result.filter(user => user.usage?.totalChars && user.usage.totalChars > 0)
    } else if (usageFilter.value.activeFilter === 'inactive') {
      result = result.filter(user => !user.usage?.totalChars || user.usage.totalChars === 0)
    } else if (usageFilter.value.activeFilter === 'vip') {
      result = result.filter(user => user.vip && user.vip.expireAt > Date.now())
    } else if (usageFilter.value.activeFilter === 'non-vip') {
      result = result.filter(user => !user.vip || user.vip.expireAt <= Date.now())
    }

    // 排序
    if (usageSort.value.field) {
      result.sort((a, b) => {
        const field = usageSort.value.field!
        let valueA: any = getNestedValue(a, field)
        let valueB: any = getNestedValue(b, field)

        // 特殊处理VIP字段
        if (field === 'vip.expireAt') {
          valueA = a.vip ? a.vip.expireAt : -1
          valueB = b.vip ? b.vip.expireAt : -1
        }

        if (valueA === valueB) return 0
        
        const modifier = usageSort.value.direction === 'asc' ? 1 : -1
        return valueA > valueB ? modifier : -modifier
      })
    }

    return result
  })

  // 工具函数 - 获取嵌套对象值
  const getNestedValue = (obj: any, path: string): any => {
    return path.split('.').reduce((current, key) => 
      current ? current[key] : undefined, obj)
  }

  // 动作 - 加载用户列表
  const loadUsers = async () => {
    isLoadingUsers.value = true
    usersError.value = null

    try {
      const data = await UserService.getUsers()
      users.value = data
    } catch (error) {
      usersError.value = error instanceof Error ? error.message : '加载用户失败'
    } finally {
      isLoadingUsers.value = false
    }
  }

  // 动作 - 加载全局统计数据
  const loadGlobalStats = async () => {
    isLoadingGlobalStats.value = true
    globalStatsError.value = null

    try {
      const stats = await UserService.getGlobalStats()
      globalStats.value = stats
    } catch (error) {
      globalStatsError.value = error instanceof Error ? error.message : '加载全局统计失败'
    } finally {
      isLoadingGlobalStats.value = false
    }
  }

  // 动作 - 加载用量数据
  const loadUsageData = async (params: PaginationParams = {}, reset = false) => {
    isLoadingUsage.value = true
    usageError.value = null

    try {
      const response = await UserService.getUsageData(params)

      if (reset) {
        usageData.value = response.users
      } else {
        usageData.value.push(...response.users)
      }

      // 保留当前页统计数据（用于向后兼容）
      usageStats.value = response.stats
      usagePagination.value = {
        hasMore: response.hasMore,
        nextCursor: response.nextCursor
      }
    } catch (error) {
      usageError.value = error instanceof Error ? error.message : '加载用量数据失败'
    } finally {
      isLoadingUsage.value = false
    }
  }

  // 动作 - 设置用户排序
  const setUserSort = (field: string) => {
    if (userSort.value.field === field) {
      userSort.value.direction = userSort.value.direction === 'asc' ? 'desc' : 'asc'
    } else {
      userSort.value.field = field
      userSort.value.direction = 'asc'
    }
  }

  // 动作 - 设置用量排序
  const setUsageSort = (field: string) => {
    if (usageSort.value.field === field) {
      usageSort.value.direction = usageSort.value.direction === 'asc' ? 'desc' : 'asc'
    } else {
      usageSort.value.field = field
      usageSort.value.direction = 'asc'
    }
  }

  // 动作 - 设置用户搜索
  const setUserSearch = (searchTerm: string) => {
    userFilter.value.searchTerm = searchTerm
  }

  // 动作 - 设置用量搜索
  const setUsageSearch = (searchTerm: string) => {
    usageFilter.value.searchTerm = searchTerm
  }

  // 动作 - 设置用量筛选
  const setUsageFilter = (filter: string) => {
    usageFilter.value.activeFilter = filter
  }

  // 动作 - 清除错误
  const clearErrors = () => {
    usersError.value = null
    usageError.value = null
    globalStatsError.value = null
  }

  return {
    // 状态
    users,
    isLoadingUsers,
    usersError,
    usageData,
    isLoadingUsage,
    usageError,
    usageStats,
    usagePagination,
    globalStats,
    isLoadingGlobalStats,
    globalStatsError,
    userFilter,
    usageFilter,
    userSort,
    usageSort,
    // 计算属性
    filteredUsers,
    filteredUsageData,
    // 动作
    loadUsers,
    loadUsageData,
    loadGlobalStats,
    setUserSort,
    setUsageSort,
    setUserSearch,
    setUsageSearch,
    setUsageFilter,
    clearErrors
  }
})
