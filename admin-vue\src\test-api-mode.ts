/**
 * API模式管理测试
 */

import { APIModeManager } from './services/apiModeManager'
import { APIRouter } from './services/apiRouter'

// 测试API模式管理
console.log('=== API模式管理测试 ===')

// 测试默认模式
console.log('默认模式:', APIModeManager.getCurrentMode())
console.log('是否用户选择:', APIModeManager.isUserSelected())

// 测试模式切换
APIModeManager.setMode('legacy', true)
console.log('切换到兼容模式:', APIModeManager.getCurrentMode())
console.log('模式名称:', APIModeManager.getModeName('legacy'))
console.log('模式描述:', APIModeManager.getModeDescription('legacy'))

APIModeManager.setMode('new', true)
console.log('切换到新版API:', APIModeManager.getCurrentMode())
console.log('模式名称:', APIModeManager.getModeName('new'))

// 测试API路由
console.log('\n=== API路由测试 ===')

// 测试新版API端点
APIModeManager.setMode('new', true)
console.log('新版登录端点:', APIRouter.getEndpoint('AUTH_LOGIN'))
console.log('新版用户端点:', APIRouter.getEndpoint('ADMIN_USERS'))

// 测试兼容模式端点
APIModeManager.setMode('legacy', true)
console.log('兼容模式登录端点:', APIRouter.getEndpoint('AUTH_LOGIN'))
console.log('兼容模式用户端点:', APIRouter.getEndpoint('ADMIN_USERS'))

// 测试功能可用性
console.log('\n=== 功能可用性测试 ===')
console.log('新版API可用功能:', APIRouter.getAvailableFeatures())

APIModeManager.setMode('new', true)
console.log('新版模式可用功能数量:', APIRouter.getAvailableFeatures().length)

APIModeManager.setMode('legacy', true)
console.log('兼容模式可用功能数量:', APIRouter.getAvailableFeatures().length)

// 测试功能差异
const comparison = APIRouter.compareModesFeatures()
console.log('功能差异分析:', comparison)

console.log('=== 测试完成 ===')
