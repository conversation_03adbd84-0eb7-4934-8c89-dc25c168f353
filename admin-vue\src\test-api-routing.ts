// API路由测试脚本
import { APIModeManager } from '@/services/apiModeManager'
import { APIRouter } from '@/services/apiRouter'

console.log('=== API路由测试 ===')

// 测试新版API模式
console.log('\n--- 新版API模式 ---')
APIModeManager.setMode('new', true)
console.log('当前模式:', APIModeManager.getModeName(APIModeManager.getCurrentMode()))
console.log('登录端点:', APIRouter.getEndpoint('AUTH_LOGIN'))
console.log('用户列表端点:', APIRouter.getEndpoint('ADMIN_USERS'))
console.log('卡密列表端点:', APIRouter.getEndpoint('ADMIN_CARDS'))
console.log('用量统计端点:', APIRouter.getEndpoint('USER_STATS'))
console.log('系统统计端点:', APIRouter.getEndpoint('ADMIN_STATS'))

// 测试兼容模式
console.log('\n--- 兼容模式 ---')
APIModeManager.setMode('legacy', true)
console.log('当前模式:', APIModeManager.getModeName(APIModeManager.getCurrentMode()))
console.log('登录端点:', APIRouter.getEndpoint('AUTH_LOGIN'))
console.log('用户列表端点:', APIRouter.getEndpoint('ADMIN_USERS'))
console.log('卡密列表端点:', APIRouter.getEndpoint('ADMIN_CARDS'))
console.log('用量统计端点:', APIRouter.getEndpoint('USER_STATS'))
console.log('系统统计端点:', APIRouter.getEndpoint('ADMIN_STATS'))

// 测试功能可用性
console.log('\n--- 功能可用性测试 ---')
APIModeManager.setMode('new', true)
const newFeatures = APIRouter.getAvailableFeatures()
console.log('新版API可用功能数量:', newFeatures.length)

APIModeManager.setMode('legacy', true)
const legacyFeatures = APIRouter.getAvailableFeatures()
console.log('兼容模式可用功能数量:', legacyFeatures.length)

// 比较功能差异
const comparison = APIRouter.compareModesFeatures()
console.log('\n--- 功能差异分析 ---')
console.log('新版独有功能:', comparison.newOnly)
console.log('兼容模式独有功能:', comparison.legacyOnly)
console.log('共同功能:', comparison.common)

console.log('\n=== 测试完成 ===')
