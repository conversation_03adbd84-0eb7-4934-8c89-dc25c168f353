// 图标导入测试
// 用于验证所有使用的图标都能正确导入

import {
  PersonOutline,
  LogOutOutline,
  PeopleOutline,
  BarChartOutline,
  CardOutline,
  RefreshOutline,
  TrashOutline,
  CreateOutline,
  CopyOutline,
  DocumentTextOutline,
  TrendingUpOutline,
  StarOutline
} from '@vicons/ionicons5'

// 导出所有图标以验证它们存在
export const AVAILABLE_ICONS = {
  PersonOutline,
  LogOutOutline,
  PeopleOutline,
  BarChartOutline,
  CardOutline,
  RefreshOutline,
  TrashOutline,
  CreateOutline,
  CopyOutline,
  DocumentTextOutline,
  TrendingUpOutline,
  StarOutline
}

// 验证图标是否可用
export const validateIcons = () => {
  const missingIcons: string[] = []
  
  Object.entries(AVAILABLE_ICONS).forEach(([name, icon]) => {
    if (!icon) {
      missingIcons.push(name)
    }
  })
  
  if (missingIcons.length > 0) {
    console.error('缺失的图标:', missingIcons)
    return false
  }
  
  console.log('所有图标验证通过')
  return true
}

// VIP相关图标映射
export const VIP_ICONS = {
  vip: StarOutline,        // VIP状态图标
  edit: CreateOutline,     // 编辑图标
  user: PersonOutline,     // 用户图标
  refresh: RefreshOutline  // 刷新图标
}
