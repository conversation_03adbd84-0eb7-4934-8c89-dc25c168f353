/**
 * 响应式工具类
 * 提供移动端检测、屏幕尺寸判断等功能
 */

import { ref, computed, onMounted, onUnmounted } from 'vue'

// 屏幕断点定义
export const BREAKPOINTS = {
  xs: 480,    // 超小屏幕
  sm: 768,    // 小屏幕
  md: 1024,   // 中等屏幕
  lg: 1200,   // 大屏幕
  xl: 1600    // 超大屏幕
} as const

// 全局窗口宽度状态
const windowWidth = ref(typeof window !== 'undefined' ? window.innerWidth : 1024)

/**
 * 响应式Hook
 * 提供屏幕尺寸检测和响应式状态
 */
export function useResponsive() {
  // 更新窗口宽度
  const updateWidth = () => {
    windowWidth.value = window.innerWidth
  }

  // 各种屏幕尺寸判断
  const isMobile = computed(() => windowWidth.value <= BREAKPOINTS.sm)
  const isTablet = computed(() => windowWidth.value > BREAKPOINTS.sm && windowWidth.value <= BREAKPOINTS.md)
  const isDesktop = computed(() => windowWidth.value > BREAKPOINTS.md)
  const isLargeScreen = computed(() => windowWidth.value >= BREAKPOINTS.lg)
  const isExtraLargeScreen = computed(() => windowWidth.value >= BREAKPOINTS.xl)

  // 具体断点判断
  const isXs = computed(() => windowWidth.value <= BREAKPOINTS.xs)
  const isSm = computed(() => windowWidth.value > BREAKPOINTS.xs && windowWidth.value <= BREAKPOINTS.sm)
  const isMd = computed(() => windowWidth.value > BREAKPOINTS.sm && windowWidth.value <= BREAKPOINTS.md)
  const isLg = computed(() => windowWidth.value > BREAKPOINTS.md && windowWidth.value <= BREAKPOINTS.lg)
  const isXl = computed(() => windowWidth.value > BREAKPOINTS.lg)

  // 当前屏幕类型
  const screenType = computed(() => {
    if (isXs.value) return 'xs'
    if (isSm.value) return 'sm'
    if (isMd.value) return 'md'
    if (isLg.value) return 'lg'
    return 'xl'
  })

  // 生命周期管理
  onMounted(() => {
    window.addEventListener('resize', updateWidth)
    updateWidth() // 初始化
  })

  onUnmounted(() => {
    window.removeEventListener('resize', updateWidth)
  })

  return {
    windowWidth: computed(() => windowWidth.value),
    isMobile,
    isTablet,
    isDesktop,
    isLargeScreen,
    isExtraLargeScreen,
    isXs,
    isSm,
    isMd,
    isLg,
    isXl,
    screenType
  }
}

/**
 * 触摸设备检测
 */
export function isTouchDevice(): boolean {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0
}

/**
 * 移动设备检测（基于User Agent）
 */
export function isMobileDevice(): boolean {
  const userAgent = navigator.userAgent.toLowerCase()
  const mobileKeywords = [
    'android', 'webos', 'iphone', 'ipad', 'ipod', 
    'blackberry', 'windows phone', 'mobile'
  ]
  return mobileKeywords.some(keyword => userAgent.includes(keyword))
}

/**
 * 获取安全的触摸目标尺寸
 * 根据设备类型返回推荐的最小触摸目标尺寸
 */
export function getSafeTouchTargetSize() {
  const { isMobile, isXs } = useResponsive()
  
  if (isXs.value) {
    return { minWidth: 48, minHeight: 52 }
  } else if (isMobile.value) {
    return { minWidth: 44, minHeight: 48 }
  } else {
    return { minWidth: 40, minHeight: 44 }
  }
}

/**
 * 获取响应式内边距
 */
export function getResponsivePadding() {
  const { screenType } = useResponsive()
  
  const paddingMap = {
    xs: '8px',
    sm: '12px',
    md: '20px',
    lg: '40px',
    xl: '60px'
  }
  
  return paddingMap[screenType.value]
}

/**
 * 获取响应式字体大小
 */
export function getResponsiveFontSize(baseSize: number = 16) {
  const { screenType } = useResponsive()
  
  const scaleMap = {
    xs: 0.875,  // 14px
    sm: 0.9375, // 15px
    md: 1,      // 16px
    lg: 1.0625, // 17px
    xl: 1.125   // 18px
  }
  
  return `${baseSize * scaleMap[screenType.value]}px`
}

/**
 * 防抖Hook
 * 用于优化resize事件等高频触发的场景
 */
export function useDebounce<T extends (...args: any[]) => any>(
  fn: T,
  delay: number = 300
): (...args: Parameters<T>) => void {
  let timeoutId: number | null = null

  return (...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }

    timeoutId = setTimeout(() => {
      fn(...args)
    }, delay)
  }
}

/**
 * 节流Hook
 * 用于限制函数执行频率
 */
export function useThrottle<T extends (...args: any[]) => any>(
  fn: T,
  delay: number = 300
): (...args: Parameters<T>) => void {
  let lastCall = 0
  
  return (...args: Parameters<T>) => {
    const now = Date.now()
    
    if (now - lastCall >= delay) {
      lastCall = now
      fn(...args)
    }
  }
}

/**
 * 媒体查询Hook
 * 提供编程式的媒体查询功能
 */
export function useMediaQuery(query: string) {
  const matches = ref(false)
  
  onMounted(() => {
    const mediaQuery = window.matchMedia(query)
    matches.value = mediaQuery.matches
    
    const handler = (e: MediaQueryListEvent) => {
      matches.value = e.matches
    }
    
    mediaQuery.addEventListener('change', handler)
    
    onUnmounted(() => {
      mediaQuery.removeEventListener('change', handler)
    })
  })
  
  return matches
}

/**
 * 视口高度Hook
 * 处理移动端视口高度变化问题
 */
export function useViewportHeight() {
  const vh = ref(window.innerHeight)
  
  const updateVh = () => {
    vh.value = window.innerHeight
    // 设置CSS自定义属性
    document.documentElement.style.setProperty('--vh', `${vh.value * 0.01}px`)
  }
  
  onMounted(() => {
    updateVh()
    window.addEventListener('resize', updateVh)
    window.addEventListener('orientationchange', updateVh)
  })
  
  onUnmounted(() => {
    window.removeEventListener('resize', updateVh)
    window.removeEventListener('orientationchange', updateVh)
  })
  
  return { vh: computed(() => vh.value) }
}
