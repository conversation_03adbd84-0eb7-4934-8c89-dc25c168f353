<template>
  <div class="login-page">
    <div class="login-container">
      <n-card class="login-card" size="large">
        <div class="login-header">
          <div class="login-logo">
            <n-icon :component="SettingsOutline" size="48" />
          </div>
          <h1 class="login-title">TTS Cards 管理系统</h1>
          <p class="login-subtitle">管理员登录 - 请输入您的管理员凭据</p>
        </div>

        <n-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          size="large"
          @submit.prevent="handleLogin"
        >
          <!-- API模式选择器 -->
          <div class="mode-selector">
            <n-space align="center" justify="space-between">
              <span class="mode-label">API模式：</span>
              <n-radio-group v-model:value="apiMode" @update:value="handleModeChange" size="small">
                <n-space>
                  <n-radio value="new">
                    <n-space align="center" :size="4">
                      <n-icon :component="CloudOutline" />
                      <span>新版API</span>
                      <n-tag size="tiny" type="success">推荐</n-tag>
                    </n-space>
                  </n-radio>
                  <n-radio value="legacy">
                    <n-space align="center" :size="4">
                      <n-icon :component="ServerOutline" />
                      <span>兼容模式</span>
                      <n-tag size="tiny" type="warning">旧版</n-tag>
                    </n-space>
                  </n-radio>
                </n-space>
              </n-radio-group>
            </n-space>
          </div>

          <n-divider />

          <!-- 新版API登录表单 -->
          <div v-if="apiMode === 'new'" class="new-api-form">
            <n-form-item path="username" label="管理员用户名">
              <n-input
                v-model:value="formData.username"
                placeholder="请输入管理员用户名"
                :disabled="isLoading"
              >
                <template #prefix>
                  <n-icon :component="PersonOutline" />
                </template>
              </n-input>
            </n-form-item>

            <n-form-item path="password" label="密码">
              <n-input
                v-model:value="formData.password"
                type="password"
                placeholder="请输入密码"
                :disabled="isLoading"
                show-password-on="mousedown"
                @keyup.enter="handleLogin"
              >
                <template #prefix>
                  <n-icon :component="KeyOutline" />
                </template>
              </n-input>
            </n-form-item>
          </div>

          <!-- 兼容模式登录表单 -->
          <div v-else class="legacy-api-form">
            <n-form-item path="username" label="用户名（可选）">
              <n-input
                v-model:value="formData.username"
                placeholder="请输入用户名（默认为admin）"
                :disabled="isLoading"
              >
                <template #prefix>
                  <n-icon :component="PersonOutline" />
                </template>
              </n-input>
            </n-form-item>

            <n-form-item path="auth_code" label="授权码">
              <n-input
                v-model:value="formData.auth_code"
                type="password"
                placeholder="请输入授权码"
                :disabled="isLoading"
                show-password-on="mousedown"
                @keyup.enter="handleLogin"
              >
                <template #prefix>
                  <n-icon :component="KeyOutline" />
                </template>
              </n-input>
            </n-form-item>
          </div>



          <n-form-item>
            <n-button
              type="primary"
              size="large"
              block
              :loading="isLoading"
              @click="handleLogin"
            >
              登录
            </n-button>
          </n-form-item>
        </n-form>

        <!-- 错误提示 -->
        <div v-if="authStore.error" class="error-message">
          <n-alert type="error" :title="authStore.error" closable @close="authStore.clearError" />
        </div>

        <div class="login-footer">
          <n-alert type="info" :show-icon="false">
            <template #header>
              <n-icon :component="InformationCircleOutline" />
              认证说明
            </template>
            <div>
              <p>• 管理员用户名必须在 ADMIN_USERS 环境变量中配置</p>
              <p>• 使用用户名+密码登录获取JWT Token</p>
              <p>• Token有效期：2小时</p>
            </div>
          </n-alert>
        </div>
      </n-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import {
  NCard,
  NForm,
  NFormItem,
  NInput,
  NButton,
  NIcon,
  NAlert,
  NRadioGroup,
  NRadio,
  NSpace,
  NTag,
  NDivider,
  useMessage,
  type FormInst,
  type FormRules
} from 'naive-ui'
import {
  SettingsOutline,
  PersonOutline,
  KeyOutline,
  InformationCircleOutline,
  CloudOutline,
  ServerOutline
} from '@vicons/ionicons5'
import { useAuthStore } from '@/stores'
import { APIModeManager, type APIMode } from '@/services/apiModeManager'
import type { AuthRequest } from '@/types'

// 路由
const router = useRouter()

// 状态管理
const authStore = useAuthStore()

// 表单引用
const formRef = ref<FormInst | null>(null)

// 表单数据
const formData = reactive({
  username: '',
  password: '',
  auth_code: ''  // 保留用于兼容模式
})

// API模式管理
const apiMode = ref<APIMode>(APIModeManager.getCurrentMode())

// 消息提示
const message = useMessage()

// 表单验证规则
const formRules: FormRules = {
  username: [
    {
      required: true,
      message: '请输入管理员用户名',
      trigger: ['input', 'blur']
    }
  ],
  password: [
    {
      required: true,
      message: '请输入密码',
      trigger: ['input', 'blur']
    }
  ],
  auth_code: [
    {
      required: false,  // 兼容模式下可选
      message: '请输入授权码',
      trigger: ['input', 'blur']
    }
  ]
}

// 加载状态
const isLoading = ref(false)

// 处理API模式切换
const handleModeChange = (mode: APIMode) => {
  apiMode.value = mode
  console.log(`API模式切换为: ${APIModeManager.getModeName(mode)}`)

  // 清空表单数据，避免模式切换时的数据混乱
  formData.username = ''
  formData.password = ''
  formData.auth_code = ''
}

// 处理登录
const handleLogin = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    isLoading.value = true

    // 根据当前API模式选择登录方式
    let loginData: AuthRequest

    if (apiMode.value === 'legacy') {
      // 兼容模式：授权码登录
      if (!formData.auth_code) {
        throw new Error('请输入授权码')
      }
      console.log('使用兼容模式登录（授权码）')
      loginData = {
        username: formData.username || 'admin',
        password: '',
        auth_code: formData.auth_code
      }
    } else {
      // 新API格式：用户名+密码
      if (!formData.username || !formData.password) {
        throw new Error('请输入用户名和密码')
      }
      console.log('使用新版API登录（用户名+密码）')
      loginData = {
        username: formData.username,
        password: formData.password
      }
    }

    const success = await authStore.login(loginData, apiMode.value)

    if (success) {
      console.log('登录成功')

      // 等待状态更新完成后再跳转
      await nextTick()

      // 使用replace而不是push，避免返回到登录页
      await router.replace('/users')

      console.log('跳转完成')
    } else {
      console.error('登录失败:', authStore.error || '登录失败')
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    isLoading.value = false
  }
}


</script>

<style scoped>
.login-page {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  overflow: auto;
}

.login-container {
  width: 100%;
  max-width: 420px;
  z-index: 1;
}

.login-card {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
  border-radius: 16px !important;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
  padding: 8px 0;
}

.login-logo {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 20px;
  margin: 0 auto 16px auto;
  color: white;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.login-title {
  margin: 0 0 8px 0;
  font-size: 26px;
  font-weight: 700;
  color: #1f2937;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
}

.login-subtitle {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
}

.login-footer {
  margin-top: 24px;
}

.login-footer p {
  margin: 4px 0;
  font-size: 13px;
  line-height: 1.4;
}

.mode-selector {
  margin-bottom: 16px;
  padding: 16px;
  background: rgba(59, 130, 246, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.mode-label {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.new-api-form {
  border-left: 3px solid #18a058;
  background: rgba(24, 160, 88, 0.05);
  border-radius: 6px;
  padding: 16px;
  margin-top: 8px;
}

.legacy-api-form {
  border-left: 3px solid #f0a020;
  background: rgba(240, 160, 32, 0.05);
  border-radius: 6px;
  padding: 16px;
  margin-top: 8px;
}

.error-message {
  margin: 16px 0;
}

:deep(.n-card__content) {
  padding: 32px !important;
}

:deep(.n-form) {
  margin: 0;
}

:deep(.n-form-item) {
  margin-bottom: 20px;
}

:deep(.n-form-item-label) {
  font-weight: 600;
  color: #374151 !important;
  margin-bottom: 8px;
}

:deep(.n-input) {
  border-radius: 8px !important;
  height: 44px;
}

:deep(.n-input__input-el) {
  font-size: 14px;
}

:deep(.n-button) {
  border-radius: 8px !important;
  height: 48px !important;
  font-weight: 600 !important;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
  border: none !important;
  font-size: 16px !important;
}

:deep(.n-button:hover) {
  background: linear-gradient(135deg, #2563eb, #1e40af) !important;
}

:deep(.n-button:focus) {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
}

:deep(.n-alert) {
  border-radius: 8px !important;
  background: rgba(59, 130, 246, 0.1) !important;
  border: 1px solid rgba(59, 130, 246, 0.2) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-page {
    padding: 16px;
  }

  .login-container {
    max-width: 100%;
  }

  :deep(.n-card__content) {
    padding: 24px !important;
  }
}

@media (max-width: 480px) {
  .login-page {
    padding: 12px;
  }

  .login-title {
    font-size: 22px;
  }

  .login-subtitle {
    font-size: 13px;
  }

  .login-logo {
    width: 64px;
    height: 64px;
  }

  :deep(.n-card__content) {
    padding: 20px !important;
  }

  :deep(.n-input) {
    height: 40px !important;
  }

  :deep(.n-button) {
    height: 44px !important;
    font-size: 15px !important;
  }
}

/* 确保在小屏幕上也能正常显示 */
@media (max-height: 600px) {
  .login-page {
    align-items: flex-start;
    padding-top: 40px;
  }
}
</style>
