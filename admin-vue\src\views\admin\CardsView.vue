<template>
  <div class="cards-view">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">
        <n-icon :component="CardOutline" />
        卡密管理
      </h2>
      <p class="page-description">生成和管理系统卡密</p>
    </div>

    <!-- 标签页 -->
    <n-tabs v-model:value="activeTab" type="line" size="large">
      <n-tab-pane name="generate" tab="生成卡密">
        <CardGenerator />
      </n-tab-pane>
      
      <n-tab-pane name="query" tab="卡密查询">
        <CardQuery />
      </n-tab-pane>
    </n-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onActivated, onBeforeUnmount } from 'vue'
import { NTabs, NTabPane, NIcon } from 'naive-ui'
import { CardOutline } from '@vicons/ionicons5'
import CardGenerator from './components/CardGenerator.vue'
import CardQuery from './components/CardQuery.vue'

// 当前激活的标签页
const activeTab = ref('generate')

// 组件生命周期管理
onMounted(() => {
  // 卡密管理页面挂载
})

onActivated(() => {
  // 卡密管理页面激活
})

onBeforeUnmount(() => {
  // 卡密管理页面卸载前清理
})
</script>

<style scoped>
.cards-view {
  width: 100%;
  margin: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
}

.page-description {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

/* 标签页样式优化 */
:deep(.n-tabs-nav) {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 12px 12px 0 0;
  padding: 8px 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-bottom: 1px solid #e2e8f0;
}

:deep(.n-tabs-tab) {
  padding: 12px 20px;
  margin: 0 4px;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
  position: relative;
}

:deep(.n-tabs-tab:hover) {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

:deep(.n-tabs-tab--active) {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transform: translateY(-1px);
}

:deep(.n-tabs-tab--active:hover) {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
}

:deep(.n-tabs-bar) {
  display: none;
}

:deep(.n-tabs-pane-wrapper) {
  background: white;
  border-radius: 0 0 12px 12px;
  padding: 24px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

:deep(.n-tabs-tab) {
  font-weight: 600;
  font-size: 16px;
}

:deep(.n-tabs-tab--active) {
  color: #3b82f6;
}

:deep(.n-tabs-tab-pane) {
  background: white;
  border-radius: 0 0 12px 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 24px;
}

/* 响应式 */
@media (max-width: 768px) {
  .page-title {
    font-size: 20px;
  }
  
  :deep(.n-tabs-nav) {
    padding: 0 16px;
  }
  
  :deep(.n-tabs-tab-pane) {
    padding: 16px;
  }
}
</style>
