<template>
  <div class="users-view">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">
        <n-icon :component="PeopleOutline" />
        用户管理
      </h2>
      <p class="page-description">管理系统用户信息和配额</p>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <SearchInput
        v-model="searchTerm"
        placeholder="搜索用户名、邮箱、VIP状态..."
        @search="handleSearch"
      />
      
      <n-button
        type="primary"
        @click="handleRefresh"
        :loading="isLoading"
      >
        <template #icon>
          <n-icon :component="RefreshOutline" />
        </template>
        刷新数据
      </n-button>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <DataTable
        :data="filteredUsers"
        :columns="tableColumns"
        :loading="isLoading"
        row-key="username"
        :pagination="{
          page: currentPage,
          pageSize: pageSize,
          showSizePicker: true,
          pageSizes: [10, 20, 50, 100],
          itemCount: filteredUsers.length
        }"
        @update:page="handlePageChange"
        @update:pageSize="handlePageSizeChange"
      />
    </div>

    <!-- 错误提示 -->
    <n-alert
      v-if="error"
      type="error"
      :title="error"
      closable
      @close="clearError"
      style="margin-top: 16px"
    />

    <!-- VIP编辑模态框 -->
    <VipEditModal
      ref="vipEditModalRef"
      v-model:show="showVipEditModal"
      :user="editingUser"
      @success="handleVipEditSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onActivated, onBeforeUnmount, h, ref } from 'vue'
import { NIcon, NButton, NAlert, NTag, useMessage } from 'naive-ui'
import { PeopleOutline, RefreshOutline, CreateOutline } from '@vicons/ionicons5'
import { DataTable, SearchInput } from '@/components/common'
import VipEditModal from './components/VipEditModal.vue'
import { useUsersStore } from '@/stores'
import { UserService } from '@/api'
import type { TableColumn, User, UpdateVipRequest } from '@/types'

// 状态管理
const usersStore = useUsersStore()
const message = useMessage()

// 分页状态
const currentPage = ref(1)
const pageSize = ref(20)

// VIP编辑相关状态
const showVipEditModal = ref(false)
const editingUser = ref<User | null>(null)
const vipEditModalRef = ref<InstanceType<typeof VipEditModal> | null>(null)

// 计算属性
const filteredUsers = computed(() => usersStore.filteredUsers)
const isLoading = computed(() => usersStore.isLoadingUsers)
const error = computed(() => usersStore.usersError)
const searchTerm = computed({
  get: () => usersStore.userFilter.searchTerm,
  set: (value: string) => usersStore.setUserSearch(value)
})

// 表格列配置
const tableColumns: TableColumn[] = [
  {
    key: 'username',
    title: '用户名',
    sortable: true,
    width: 150
  },
  {
    key: 'email',
    title: '邮箱',
    sortable: true,
    width: 200,
    render: (row) => {
      return row.email || '-'
    }
  },
  {
    key: 'createdAt',
    title: '创建时间',
    sortable: true,
    width: 180,
    render: (row) => {
      // 支持ISO字符串和时间戳
      const time = row.createdAt || row.createAt
      if (!time) return '-'

      const date = typeof time === 'string' ? new Date(time) : new Date(time)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  },
  {
    key: 'usage.totalChars',
    title: '总字符使用',
    sortable: true,
    width: 120,
    render: (row) => {
      const totalChars = row.usage?.totalChars || row.quota?.used || 0
      return h('span', { style: 'font-weight: 600; color: #3b82f6;' }, totalChars.toLocaleString())
    }
  },
  {
    key: 'usage.monthlyChars',
    title: '月度使用',
    sortable: true,
    width: 120,
    render: (row) => {
      const monthlyChars = row.usage?.monthlyChars || 0
      return h('span', { style: 'font-weight: 600; color: #10b981;' }, monthlyChars.toLocaleString())
    }
  },
  {
    key: 'vip.quotaChars',
    title: 'VIP配额',
    sortable: true,
    width: 120,
    render: (row) => {
      if (row.vip && row.vip.quotaChars) {
        const used = row.vip.usedChars || 0
        const total = row.vip.quotaChars
        const percentage = total > 0 ? (used / total * 100).toFixed(1) : '0'

        let color = '#10b981' // 绿色
        if (parseFloat(percentage) > 80) {
          color = '#ef4444' // 红色
        } else if (parseFloat(percentage) > 60) {
          color = '#f59e0b' // 黄色
        }

        return h('div', [
          h('span', { style: `font-weight: 600; color: ${color};` }, `${used.toLocaleString()}/${total.toLocaleString()}`),
          h('span', { style: 'font-size: 12px; color: #6b7280; margin-left: 4px;' }, `(${percentage}%)`)
        ])
      }
      return h('span', { style: 'color: #6b7280;' }, '-')
    }
  },
  {
    key: 'vip',
    title: 'VIP状态',
    sortable: true,
    width: 160,
    render: (row) => {
      if (row.vip && row.vip.type) {
        const expireDate = new Date(row.vip.expireAt).toLocaleDateString()
        const isExpired = row.vip.isExpired !== undefined ? row.vip.isExpired : (row.vip.expireAt < Date.now())

        return h(NTag, {
          type: isExpired ? 'error' : 'success',
          size: 'small'
        }, {
          default: () => `${row.vip.type} - ${expireDate}`
        })
      } else {
        return h(NTag, {
          type: 'default',
          size: 'small'
        }, {
          default: () => '非VIP'
        })
      }
    }
  },
  {
    key: 'actions',
    title: '操作',
    width: 120,
    render: (row) => {
      return h(NButton, {
        size: 'small',
        type: 'primary',
        secondary: true,
        onClick: () => handleEditVip(row)
      }, {
        icon: () => h(NIcon, { component: CreateOutline }),
        default: () => '编辑VIP'
      })
    }
  }
]

// 处理搜索
const handleSearch = (value: string) => {
  usersStore.setUserSearch(value)
}

// 处理分页变化
const handlePageChange = (page: number) => {
  currentPage.value = page
}

// 处理页面大小变化
const handlePageSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1 // 重置到第一页
}

// 处理刷新
const handleRefresh = async () => {
  try {
    await usersStore.loadUsers()
  } catch (error) {
    console.error('数据刷新失败:', error)
  }
}

// 清除错误
const clearError = () => {
  usersStore.clearErrors()
}

// VIP编辑相关方法
const handleEditVip = (user: User) => {
  editingUser.value = user
  showVipEditModal.value = true
}

const handleVipEditSuccess = async (user: User) => {
  try {
    if (!vipEditModalRef.value) {
      message.error('表单数据获取失败')
      return
    }

    const formData = vipEditModalRef.value.formData as UpdateVipRequest

    // 调用API更新VIP状态
    const response = await UserService.updateUserVip(user.username, formData)

    if (response.success) {
      message.success('VIP状态更新成功')
      showVipEditModal.value = false

      // 刷新用户列表
      await usersStore.loadUsers()
    } else {
      message.error(response.message || 'VIP状态更新失败')
    }
  } catch (error) {
    console.error('VIP状态更新失败:', error)
    message.error(error instanceof Error ? error.message : 'VIP状态更新失败')
  }
}

// 组件挂载时加载数据
onMounted(() => {
  usersStore.loadUsers()
})

// 组件激活时不自动加载数据，改为手动刷新模式
onActivated(() => {
  // 使用缓存数据，需要时请手动刷新
})

// 组件卸载前清理
onBeforeUnmount(() => {
  usersStore.clearErrors()
})
</script>

<style scoped>
.users-view {
  width: 100%;
  margin: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
}

.page-description {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.search-section {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.table-section {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* 响应式 */
@media (max-width: 768px) {
  .search-section {
    flex-direction: column;
    align-items: stretch;
  }
  
  .page-title {
    font-size: 20px;
  }
}
</style>
