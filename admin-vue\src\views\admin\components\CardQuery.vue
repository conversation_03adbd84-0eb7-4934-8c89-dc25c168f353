<template>
  <div class="card-query">
    <!-- 搜索区域 -->
    <div class="search-section">
      <SearchInput
        v-model="searchTerm"
        placeholder="搜索卡密号、类型、状态、使用者..."
        @search="handleSearch"
      />
      
      <n-button
        type="primary"
        @click="handleRefresh"
        :loading="isLoading"
      >
        <template #icon>
          <n-icon :component="RefreshOutline" />
        </template>
        刷新数据
      </n-button>
    </div>

    <!-- 批量操作区域 -->
    <div v-if="selectedCards.length > 0" class="batch-actions">
      <n-alert type="info" :show-icon="false">
        <template #header>
          已选择 {{ selectedCards.length }} 个卡密
        </template>
        
        <div class="batch-buttons">
          <n-button
            type="error"
            size="small"
            @click="handleBatchDelete"
            :loading="isBatchDeleting"
          >
            <template #icon>
              <n-icon :component="TrashOutline" />
            </template>
            批量删除
          </n-button>
          
          <n-button
            size="small"
            @click="clearSelection"
          >
            取消选择
          </n-button>
        </div>
      </n-alert>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <DataTable
        :data="filteredCards"
        :columns="tableColumns"
        :loading="isLoading"
        :checkable="true"
        :checked-row-keys="selectedCards"
        @update:checked-row-keys="handleSelectionChange"
        :row-key="(row: Card) => `card-${String(row.code || (row as any).c || '')}-${String(row.type || (row as any).t || '')}-${String(row.status || (row as any).s || '')}`"
        :scroll-x="1700"
        :pagination="{
          page: currentPage,
          pageSize: pageSize,
          showSizePicker: true,
          pageSizes: [10, 20, 50, 100],
          itemCount: filteredCards.length
        }"
        @update:page="handlePageChange"
        @update:pageSize="handlePageSizeChange"
      />
    </div>

    <!-- 编辑弹窗 -->
    <n-modal v-model:show="showEditModal" preset="dialog" title="编辑卡密">
      <template #header>
        <div style="display: flex; align-items: center; gap: 8px;">
          <n-icon :component="CreateOutline" />
          编辑卡密
        </div>
      </template>
      
      <n-form
        ref="editFormRef"
        :model="editFormData"
        :rules="editFormRules"
        label-placement="top"
      >
        <n-form-item label="卡密号" path="newCode">
          <n-input
            v-model:value="editFormData.newCode"
            placeholder="请输入新的卡密号"
          />
        </n-form-item>
      </n-form>
      
      <template #action>
        <n-button @click="showEditModal = false">取消</n-button>
        <n-button
          type="primary"
          @click="handleSaveEdit"
          :loading="isEditing"
        >
          保存
        </n-button>
      </template>
    </n-modal>

    <!-- 错误提示 -->
    <n-alert
      v-if="error"
      type="error"
      :title="error"
      closable
      @close="clearError"
      style="margin-top: 16px"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onActivated, h, ref, reactive } from 'vue'
import {
  NButton,
  NIcon,
  NAlert,
  NTag,
  NModal,
  NForm,
  NFormItem,
  NInput,
  NPopconfirm,
  useMessage,
  type FormInst,
  type FormRules
} from 'naive-ui'
import {
  RefreshOutline,
  TrashOutline,
  CreateOutline,
  CopyOutline
} from '@vicons/ionicons5'
import { DataTable, SearchInput } from '@/components/common'
import { useCardsStore } from '@/stores'
import { CardService } from '@/api'
import type { TableColumn, Card } from '@/types'

// 状态管理
const cardsStore = useCardsStore()

// 消息提示
const message = useMessage()

// 响应式数据
const showEditModal = ref(false)
const isEditing = ref(false)
const isBatchDeleting = ref(false)
const editFormRef = ref<FormInst | null>(null)

// 分页状态
const currentPage = ref(1)
const pageSize = ref(20)

// 编辑表单数据
const editFormData = reactive({
  oldCode: '',
  newCode: '',
  type: ''
})

// 编辑表单验证规则
const editFormRules: FormRules = {
  newCode: [
    {
      required: true,
      message: '请输入卡密号',
      trigger: ['input', 'blur']
    }
  ]
}

// 计算属性
const filteredCards = computed(() => cardsStore.filteredCards)
const isLoading = computed(() => cardsStore.isLoading)
const error = computed(() => cardsStore.error)
const selectedCards = computed({
  get: () => cardsStore.selectedCards,
  set: (value: string[]) => {
    cardsStore.selectedCards = value
  }
})

const searchTerm = computed({
  get: () => cardsStore.searchTerm,
  set: (value: string) => cardsStore.setSearch(value)
})

// 表格列配置
const tableColumns: TableColumn[] = [
  {
    key: 'code',
    title: '卡密号',
    sortable: true,
    width: 250,
    minWidth: 200,
    render: (row: Card) => {
      // 安全获取卡密号，兼容新旧数据格式
      const code = String(row.code || (row as any).c || '')
      return h('span', {
        style: 'font-family: monospace; font-weight: 600; color: #1f2937;'
      }, code)
    }
  },
  {
    key: 'type',
    title: '类型',
    sortable: true,
    width: 140,
    minWidth: 100,
    render: (row: Card) => {
      // 安全获取卡密类型，兼容新旧数据格式
      const type = row.type || row.package_type || (row as any).t || 'Unknown'
      return h(NTag, {
        type: 'info',
        size: 'small'
      }, {
        default: () => CardService.getCardTypeName(type)
      })
    }
  },
  {
    key: 'status',
    title: '状态',
    sortable: true,
    width: 120,
    minWidth: 80,
    render: (row: Card) => {
      // 安全获取状态，兼容新旧数据格式
      const status = row.status || (row as any).s || 'unknown'
      const isUnused = status === 'unused'
      return h(NTag, {
        type: isUnused ? 'success' : 'error',
        size: 'small'
      }, {
        default: () => isUnused ? '未使用' : '已使用'
      })
    }
  },
  {
    key: 'usedBy',
    title: '使用者',
    width: 200,
    minWidth: 150,
    render: (row: Card) => {
      // 安全获取使用者，兼容新旧数据格式
      const usedBy = row.usedBy || row.user || (row as any).u || ''
      return usedBy || '-'
    }
  },
  {
    key: 'totalChars',
    title: '历史总使用量',
    width: 150,
    minWidth: 120,
    render: (row: Card) => {
      if (row.status === 'unused') {
        return h('span', { style: 'color: #9ca3af;' }, '-')
      }

      // 优先使用新格式的userUsage数据
      const totalChars = row.userUsage?.totalChars ?? row.totalChars

      if (totalChars !== undefined && totalChars > 0) {
        return h('span', {
          style: 'color: #1f2937; font-weight: 500;'
        }, totalChars.toLocaleString())
      }

      // 如果已使用但没有使用量数据，显示"无数据"
      return h('span', { style: 'color: #f59e0b;' }, '无数据')
    }
  },
  {
    key: 'monthlyChars',
    title: '当月使用量',
    width: 140,
    minWidth: 120,
    render: (row: Card) => {
      if (row.status === 'unused') {
        return h('span', { style: 'color: #9ca3af;' }, '-')
      }

      const monthlyChars = row.userUsage?.monthlyChars

      if (monthlyChars !== undefined && monthlyChars > 0) {
        return h('span', {
          style: 'color: #059669; font-weight: 500;'
        }, monthlyChars.toLocaleString())
      }

      if (row.userUsage) {
        // 有userUsage但monthlyChars为0，说明本月未使用
        return h('span', { style: 'color: #9ca3af;' }, '0')
      }

      // 没有userUsage数据
      return h('span', { style: 'color: #f59e0b;' }, '无数据')
    }
  },
  {
    key: 'activatedAt',
    title: '激活时间',
    sortable: true,
    width: 200,
    minWidth: 160,
    render: (row: Card) => {
      return row.activatedAt
        ? new Date(row.activatedAt).toLocaleString('zh-CN')
        : '-'
    }
  },
  {
    key: 'createdAt',
    title: '创建时间',
    sortable: true,
    width: 200,
    minWidth: 160,
    render: (row: Card) => {
      // 安全获取创建时间，兼容新旧数据格式
      const createdAt = row.createdAt || row.created_at || (row as any).created
      return createdAt ? new Date(createdAt).toLocaleString('zh-CN') : '-'
    }
  },
  {
    key: 'actions',
    title: '操作',
    width: 260,
    minWidth: 240,
    fixed: 'right',
    render: (row: Card) => {
      return h('div', {
        style: 'display: flex; gap: 6px; flex-wrap: nowrap; justify-content: flex-start;'
      }, [
        h(NButton, {
          size: 'small',
          onClick: () => copyCard(String(row.code || (row as any).c || '')),
          style: 'flex-shrink: 0;'
        }, {
          default: () => '复制',
          icon: () => h(NIcon, { component: CopyOutline })
        }),
        h(NButton, {
          size: 'small',
          type: 'primary',
          onClick: () => editCard(row),
          style: 'flex-shrink: 0;'
        }, {
          default: () => '编辑',
          icon: () => h(NIcon, { component: CreateOutline })
        }),
        h(NPopconfirm, {
          onPositiveClick: () => deleteCard(String(row.code || (row as any).c || ''))
        }, {
          default: () => '确定要删除这个卡密吗？',
          trigger: () => h(NButton, {
            size: 'small',
            type: 'error',
            style: 'flex-shrink: 0;'
          }, {
            default: () => '删除',
            icon: () => h(NIcon, { component: TrashOutline })
          })
        })
      ])
    }
  }
]

// 处理分页变化
const handlePageChange = (page: number) => {
  currentPage.value = page
  console.log('卡密查询页码变化:', page)
}

// 处理页面大小变化
const handlePageSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1 // 重置到第一页
  console.log('卡密查询页面大小变化:', size)
}

// 处理搜索
const handleSearch = (value: string) => {
  cardsStore.setSearch(value)
}

// 处理刷新
const handleRefresh = async () => {
  try {
    await cardsStore.loadCards()
    message.success('数据刷新成功')
  } catch (error) {
    console.error('数据刷新失败:', error)
    message.error(error instanceof Error ? error.message : '数据刷新失败')
  }
}

// 处理选择变化
const handleSelectionChange = (keys: (string | number)[]) => {
  // 更新选中的卡密
  keys.forEach(key => {
    const keyStr = String(key)
    if (!cardsStore.selectedCards.includes(keyStr)) {
      cardsStore.toggleCardSelection(keyStr)
    }
  })
  
  // 移除未选中的卡密
  cardsStore.selectedCards.forEach(key => {
    if (!keys.includes(key)) {
      cardsStore.toggleCardSelection(key)
    }
  })
}

// 复制卡密
const copyCard = async (code: string) => {
  try {
    await navigator.clipboard.writeText(code)
    message.success('卡密已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    message.error('复制失败，请手动复制')
  }
}

// 编辑卡密
const editCard = (card: Card) => {
  // 安全获取卡密数据，兼容新旧数据格式
  const code = String(card.code || (card as any).c || '')
  const type = card.type || card.package_type || (card as any).t || 'Unknown'

  editFormData.oldCode = code
  editFormData.newCode = code
  editFormData.type = type
  showEditModal.value = true
}

// 保存编辑
const handleSaveEdit = async () => {
  if (!editFormRef.value) return

  try {
    await editFormRef.value.validate()
    isEditing.value = true

    await cardsStore.editCard(
      editFormData.oldCode,
      editFormData.newCode,
      editFormData.type
    )

    message.success('卡密编辑成功')
    showEditModal.value = false
  } catch (error) {
    console.error('卡密编辑失败:', error)
    message.error(error instanceof Error ? error.message : '卡密编辑失败')
  } finally {
    isEditing.value = false
  }
}

// 删除卡密
const deleteCard = async (code: string) => {
  try {
    await cardsStore.deleteCard(code)
    message.success(`卡密 ${code} 删除成功`)
  } catch (error) {
    console.error('卡密删除失败:', error)
    message.error(error instanceof Error ? error.message : '卡密删除失败')
  }
}

// 批量删除
const handleBatchDelete = async () => {
  const selectedCount = selectedCards.value.length
  isBatchDeleting.value = true

  try {
    await cardsStore.deleteSelectedCards()
    message.success(`成功删除 ${selectedCount} 个卡密`)
  } catch (error) {
    console.error('批量删除失败:', error)
    message.error(error instanceof Error ? error.message : '批量删除失败')
  } finally {
    isBatchDeleting.value = false
  }
}

// 清除选择
const clearSelection = () => {
  cardsStore.selectedCards.forEach(code => {
    cardsStore.toggleCardSelection(code)
  })
}

// 清除错误
const clearError = () => {
  cardsStore.clearError()
}

// 组件挂载时加载数据
onMounted(() => {
  cardsStore.loadCards()
})

// 组件激活时不自动加载数据，改为手动刷新模式
onActivated(() => {
  // 使用缓存数据，需要时请手动刷新
})
</script>

<style scoped>
.card-query {
  width: 100%;
  margin: 0;
  padding: 0 20px;
}

.search-section {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.batch-actions {
  margin-bottom: 16px;
}

.batch-buttons {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.table-section {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  width: 100%;
  margin: 0;
}

/* 响应式 */
@media (max-width: 768px) {
  .card-query {
    padding: 0 10px;
  }

  .search-section {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .batch-buttons {
    flex-direction: column;
    gap: 8px;
  }

  .batch-buttons .n-button {
    width: 100%;
    justify-content: center;
  }

  /* 移动端表格优化 */
  .table-section {
    margin: 0 -10px;
    border-radius: 0;
  }
}

/* 小屏幕设备优化 */
@media (max-width: 480px) {
  .card-query {
    padding: 0 8px;
  }

  .search-section {
    gap: 8px;
  }

  /* 搜索输入框全宽 */
  :deep(.n-input) {
    width: 100%;
  }

  /* 按钮组优化 */
  .batch-buttons {
    gap: 6px;
  }

  .batch-buttons .n-button {
    min-height: 48px;
    font-size: 14px;
  }
}

/* 平板设备优化 */
@media (min-width: 769px) and (max-width: 1024px) {
  .card-query {
    padding: 0 20px;
  }

  .search-section {
    gap: 12px;
  }
}

/* 大屏幕优化 */
@media (min-width: 1200px) {
  .card-query {
    padding: 0 0px;
  }
}

/* 超大屏幕优化 */
@media (min-width: 1600px) {
  .card-query {
    padding: 0 0px;
  }
}
</style>
