<template>
  <n-modal v-model:show="showModal" preset="dialog" title="编辑VIP状态" style="width: 500px;">
    <template #header>
      <div style="display: flex; align-items: center; gap: 8px;">
        <n-icon :component="StarOutline" />
        编辑VIP状态 - {{ user?.username }}
      </div>
    </template>
    
    <div v-if="user" class="vip-edit-content">
      <!-- 当前VIP状态显示 -->
      <div class="current-status">
        <h4>当前VIP状态</h4>
        <n-tag v-if="user.vip && user.vip.expireAt > Date.now()" type="success" size="large">
          {{ user.vip.type }} - {{ formatDate(user.vip.expireAt) }}
        </n-tag>
        <n-tag v-else type="default" size="large">
          非VIP用户
        </n-tag>
      </div>
      
      <!-- 编辑表单 -->
      <n-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-placement="top"
        style="margin-top: 20px;"
      >
        <n-form-item label="操作类型" path="action">
          <n-radio-group v-model:value="formData.action" @update:value="handleActionChange">
            <n-space>
              <n-radio value="set">设置VIP</n-radio>
              <n-radio value="extend">延长时间</n-radio>
              <n-radio value="remove">移除VIP</n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item>
        
        <!-- 设置VIP选项 -->
        <template v-if="formData.action === 'set'">
          <n-form-item label="VIP类型" path="vipType">
            <n-select 
              v-model:value="formData.vipType" 
              :options="vipTypeOptions"
              placeholder="请选择VIP类型"
              @update:value="handleVipTypeChange"
            />
          </n-form-item>
          
          <n-form-item label="到期时间" path="expireAt">
            <n-date-picker 
              v-model:value="formData.expireAt" 
              type="datetime" 
              placeholder="请选择到期时间"
              style="width: 100%;"
              :is-date-disabled="(ts: number) => ts < Date.now()"
            />
          </n-form-item>
        </template>
        
        <!-- 延长时间选项 -->
        <template v-if="formData.action === 'extend'">
          <n-form-item label="VIP类型" path="vipType">
            <n-select 
              v-model:value="formData.vipType" 
              :options="vipTypeOptions"
              placeholder="请选择VIP类型（可选）"
            />
          </n-form-item>
          
          <n-form-item label="延长天数" path="extendDays">
            <n-input-number 
              v-model:value="formData.extendDays" 
              :min="1" 
              :max="3650"
              placeholder="请输入延长天数"
              style="width: 100%;"
            />
          </n-form-item>
          
          <n-alert type="info" style="margin-top: 10px;">
            <template v-if="user.vip && user.vip.expireAt > Date.now()">
              当前到期时间：{{ formatDate(user.vip.expireAt) }}
              <br />
              延长后到期时间：{{ formData.extendDays ? formatDate(user.vip.expireAt + formData.extendDays * 24 * 60 * 60 * 1000) : '请输入延长天数' }}
            </template>
            <template v-else>
              当前无VIP，将从现在开始计算 {{ formData.extendDays || 0 }} 天
              <br />
              到期时间：{{ formData.extendDays ? formatDate(Date.now() + formData.extendDays * 24 * 60 * 60 * 1000) : '请输入延长天数' }}
            </template>
          </n-alert>
        </template>
        
        <!-- 移除VIP确认 -->
        <template v-if="formData.action === 'remove'">
          <n-alert type="warning">
            <strong>警告：</strong>此操作将移除用户的VIP状态，操作不可撤销。
          </n-alert>
        </template>
      </n-form>
    </div>
    
    <template #action>
      <n-space>
        <n-button @click="handleCancel">取消</n-button>
        <n-button
          type="primary"
          @click="handleSubmit"
          :loading="isSubmitting"
          :disabled="!isFormValid"
        >
          确认{{ getActionText() }}
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { 
  NModal, NForm, NFormItem, NRadioGroup, NRadio, NSelect, NDatePicker, 
  NInputNumber, NAlert, NButton, NSpace, NTag, NIcon, useMessage 
} from 'naive-ui'
import { StarOutline } from '@vicons/ionicons5'
import type { User, UpdateVipRequest } from '@/types'
import type { FormInst, FormRules } from 'naive-ui'
import { VIP_TYPES } from '@/constants/vip'

// Props
interface Props {
  show: boolean
  user: User | null
}

// Emits
interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'success', user: User): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Refs
const formRef = ref<FormInst | null>(null)
const message = useMessage()

// State
const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const isSubmitting = ref(false)

// Form data
const formData = ref<UpdateVipRequest & { expireAt?: number }>({
  action: 'set',
  vipType: '',
  expireAt: undefined,
  extendDays: undefined
})

// VIP类型选项 - 转换为Naive UI SelectOption格式
const vipTypeOptions = VIP_TYPES.map(type => ({
  label: type.label,
  value: type.value
}))

// Form rules
const formRules: FormRules = {
  action: {
    required: true,
    message: '请选择操作类型'
  },
  vipType: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule, value) => {
      if (formData.value.action === 'set' && !value) {
        return new Error('设置VIP时必须选择VIP类型')
      }
      return true
    }
  },
  expireAt: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule, value) => {
      if (formData.value.action === 'set' && !value) {
        return new Error('设置VIP时必须选择到期时间')
      }
      if (value && value <= Date.now()) {
        return new Error('到期时间必须大于当前时间')
      }
      return true
    }
  },
  extendDays: {
    required: true,
    trigger: ['blur', 'change'],
    validator: (rule, value) => {
      if (formData.value.action === 'extend' && (!value || value <= 0)) {
        return new Error('延长时间时必须输入有效天数')
      }
      return true
    }
  }
}

// Computed
const isFormValid = computed(() => {
  const { action, vipType, expireAt, extendDays } = formData.value
  
  switch (action) {
    case 'set':
      return vipType && expireAt && expireAt > Date.now()
    case 'extend':
      return extendDays && extendDays > 0
    case 'remove':
      return true
    default:
      return false
  }
})

// Methods
const formatDate = (timestamp: number) => {
  return new Date(timestamp).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getActionText = () => {
  switch (formData.value.action) {
    case 'set': return '设置'
    case 'extend': return '延长'
    case 'remove': return '移除'
    default: return '操作'
  }
}

const handleActionChange = () => {
  // 重置表单数据
  formData.value.vipType = ''
  formData.value.expireAt = undefined
  formData.value.extendDays = undefined
  
  // 清除验证错误
  nextTick(() => {
    formRef.value?.restoreValidation()
  })
}

const handleVipTypeChange = (value: string) => {
  if (formData.value.action === 'set' && value !== 'custom') {
    const vipType = VIP_TYPES.find(type => type.value === value)
    if (vipType?.durationDays) {
      // 自动设置到期时间
      formData.value.expireAt = Date.now() + vipType.durationDays * 24 * 60 * 60 * 1000
    }
  }
}

const handleCancel = () => {
  showModal.value = false
}

const handleSubmit = async () => {
  if (!formRef.value || !props.user) return
  
  try {
    await formRef.value.validate()
    isSubmitting.value = true
    
    // 这里会由父组件处理实际的API调用
    emit('success', props.user)
    
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    isSubmitting.value = false
  }
}

// 监听用户变化，重置表单
watch(() => props.user, (newUser) => {
  if (newUser) {
    formData.value = {
      action: 'set',
      vipType: '',
      expireAt: undefined,
      extendDays: undefined
    }
    
    nextTick(() => {
      formRef.value?.restoreValidation()
    })
  }
}, { immediate: true })

// 暴露表单数据给父组件
defineExpose({
  formData: computed(() => formData.value)
})
</script>

<style scoped>
.vip-edit-content {
  padding: 16px 0;
}

.current-status {
  padding: 16px;
  background: var(--n-color-target);
  border-radius: 6px;
  margin-bottom: 16px;
}

.current-status h4 {
  margin: 0 0 8px 0;
  color: var(--n-text-color-base);
}
</style>
