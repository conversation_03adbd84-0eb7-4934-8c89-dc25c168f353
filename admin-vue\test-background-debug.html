<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>侧边栏背景色调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        .warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            overflow-x: auto;
        }
        .debug-steps {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .debug-steps ol {
            margin: 0;
            padding-left: 20px;
        }
        .debug-steps li {
            margin: 8px 0;
            line-height: 1.5;
        }
        .css-selector {
            background: #e3f2fd;
            padding: 8px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            margin: 5px 0;
        }
        .fix-priority {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 15px 0;
        }
        .priority-high, .priority-medium {
            padding: 15px;
            border-radius: 8px;
        }
        .priority-high {
            background: #fef2f2;
            border: 1px solid #fecaca;
        }
        .priority-medium {
            background: #fffbeb;
            border: 1px solid #fed7aa;
        }
        .browser-test {
            background: #f0f9ff;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <h1>🔍 侧边栏背景色调试工具</h1>
    
    <div class="test-section error">
        <h2>❌ 问题描述</h2>
        <p>从图片可以看到，收缩状态下的菜单项仍然有背景色，尽管我们已经设置了多个透明背景样式。</p>
        <p><strong>可能的原因：</strong></p>
        <ul>
            <li>Naive UI 的内部样式优先级更高</li>
            <li>某些特定的CSS类没有被覆盖</li>
            <li>浏览器的默认样式干扰</li>
            <li>CSS选择器的特异性不够</li>
        </ul>
    </div>

    <div class="test-section warning">
        <h2>🔧 调试步骤</h2>
        <div class="debug-steps">
            <h3>第一步：打开浏览器开发者工具</h3>
            <ol>
                <li>访问 <a href="http://localhost:5174" target="_blank">http://localhost:5174</a></li>
                <li>登录管理后台</li>
                <li>按 F12 打开开发者工具</li>
                <li>确保侧边栏处于收缩状态</li>
            </ol>
        </div>

        <div class="debug-steps">
            <h3>第二步：检查菜单项元素</h3>
            <ol>
                <li>右键点击有背景色的菜单项</li>
                <li>选择"检查元素"</li>
                <li>在Elements面板中找到菜单项的DOM结构</li>
                <li>查看应用的CSS类名</li>
            </ol>
        </div>

        <div class="debug-steps">
            <h3>第三步：分析CSS样式</h3>
            <ol>
                <li>在Styles面板中查看所有应用的样式</li>
                <li>找到设置背景色的CSS规则</li>
                <li>记录具体的选择器和属性值</li>
                <li>检查是否有被覆盖的样式</li>
            </ol>
        </div>
    </div>

    <div class="test-section info">
        <h2>🎯 可能的CSS选择器</h2>
        <p>根据Naive UI的结构，背景色可能来自以下选择器：</p>
        
        <div class="css-selector">
            <strong>基础菜单项：</strong><br>
            .n-menu-item<br>
            .n-menu-item .n-menu-item-content<br>
            .n-menu-item .n-menu-item-content-header
        </div>

        <div class="css-selector">
            <strong>收缩状态：</strong><br>
            .n-menu--collapsed .n-menu-item<br>
            .n-menu--collapsed .n-menu-item-content<br>
            .n-menu--collapsed .n-menu-item:not(.n-menu-item--selected)
        </div>

        <div class="css-selector">
            <strong>状态相关：</strong><br>
            .n-menu-item:hover<br>
            .n-menu-item:focus<br>
            .n-menu-item:active<br>
            .n-menu-item--selected
        </div>

        <div class="css-selector">
            <strong>可能的内部类：</strong><br>
            .n-menu-item__content<br>
            .n-menu-item__icon<br>
            .n-menu-item__label<br>
            .n-menu-item__arrow
        </div>
    </div>

    <div class="test-section success">
        <h2>✅ 已添加的强制样式</h2>
        <div class="code-block">
/* 我们已经添加的强制透明背景样式 */

/* 基础透明设置 */
:deep(.n-menu) {
  background: transparent !important;
}

:deep(.n-menu-item) {
  background: transparent !important;
  background-color: transparent !important;
}

/* 强制覆盖所有子元素 */
:deep(.n-menu-item),
:deep(.n-menu-item .n-menu-item-content),
:deep(.n-menu-item .n-menu-item-content-header),
:deep(.n-menu-item .n-menu-item-content__arrow),
:deep(.n-menu-item .n-menu-item-content__icon) {
  background: transparent !important;
  background-color: transparent !important;
}

/* 收缩状态特殊处理 */
:deep(.n-menu--collapsed .n-menu-item:not(.n-menu-item--selected)) {
  background: transparent !important;
  background-color: transparent !important;
}

:deep(.n-menu--collapsed .n-menu-item:not(.n-menu-item--selected) *) {
  background: transparent !important;
  background-color: transparent !important;
}
        </div>
    </div>

    <div class="test-section warning">
        <h2>🚨 如果问题仍然存在</h2>
        <div class="fix-priority">
            <div class="priority-high">
                <h3>🔴 高优先级修复</h3>
                <p><strong>添加更具体的选择器：</strong></p>
                <div class="code-block" style="font-size: 11px;">
/* 使用更高特异性的选择器 */
:deep(.n-layout-sider .n-menu.n-menu--collapsed .n-menu-item:not(.n-menu-item--selected)) {
  background: transparent !important;
  background-color: transparent !important;
}

/* 覆盖所有可能的伪类状态 */
:deep(.n-menu-item:not(.n-menu-item--selected):not(:hover):not(:focus):not(:active)) {
  background: transparent !important;
}
                </div>
            </div>
            
            <div class="priority-medium">
                <h3>🟡 中优先级修复</h3>
                <p><strong>使用CSS变量覆盖：</strong></p>
                <div class="code-block" style="font-size: 11px;">
/* 覆盖Naive UI的CSS变量 */
:deep(.n-menu) {
  --n-item-color-hover: transparent;
  --n-item-color-active: transparent;
  --n-item-color: transparent;
}
                </div>
            </div>
        </div>
    </div>

    <div class="test-section info">
        <h2>🔍 实时调试工具</h2>
        <div class="browser-test">
            <h3>在浏览器控制台中运行以下代码：</h3>
            <div class="code-block">
// 查找所有菜单项元素
const menuItems = document.querySelectorAll('.n-menu-item');
console.log('找到菜单项数量:', menuItems.length);

// 检查每个菜单项的背景色
menuItems.forEach((item, index) => {
  const computedStyle = window.getComputedStyle(item);
  const bgColor = computedStyle.backgroundColor;
  const bgImage = computedStyle.backgroundImage;
  
  console.log(`菜单项 ${index + 1}:`, {
    element: item,
    backgroundColor: bgColor,
    backgroundImage: bgImage,
    classes: item.className
  });
});

// 强制设置透明背景（临时测试）
menuItems.forEach(item => {
  if (!item.classList.contains('n-menu-item--selected')) {
    item.style.setProperty('background', 'transparent', 'important');
    item.style.setProperty('background-color', 'transparent', 'important');
  }
});

console.log('已强制设置所有非选中菜单项为透明背景');
            </div>
            <p><strong>使用方法：</strong></p>
            <ol>
                <li>复制上面的代码</li>
                <li>在浏览器开发者工具的Console面板中粘贴</li>
                <li>按Enter执行</li>
                <li>查看控制台输出的背景色信息</li>
                <li>观察页面是否变化</li>
            </ol>
        </div>
    </div>

    <div class="test-section success">
        <h2>📋 问题报告模板</h2>
        <p>如果问题仍然存在，请提供以下信息：</p>
        <div class="debug-steps">
            <ol>
                <li><strong>浏览器信息：</strong> Chrome/Firefox/Safari 版本号</li>
                <li><strong>DOM结构：</strong> 菜单项的完整HTML结构</li>
                <li><strong>应用的CSS类：</strong> 所有应用到菜单项的CSS类名</li>
                <li><strong>计算样式：</strong> background-color 和 background-image 的值</li>
                <li><strong>样式来源：</strong> 设置背景色的具体CSS文件和行号</li>
                <li><strong>控制台输出：</strong> 运行调试代码后的控制台输出</li>
            </ol>
        </div>
    </div>

    <script>
        console.log('🔍 侧边栏背景色调试工具已加载');
        console.log('📝 请按照页面上的步骤进行调试');
        
        // 自动检测是否在管理后台页面
        if (window.location.href.includes('localhost:5174')) {
            console.log('✅ 检测到管理后台页面');
            console.log('💡 可以直接在此页面进行调试');
            
            // 延迟执行检查，等待页面完全加载
            setTimeout(() => {
                const menuItems = document.querySelectorAll('.n-menu-item');
                if (menuItems.length > 0) {
                    console.log(`🎯 找到 ${menuItems.length} 个菜单项`);
                    console.log('💡 可以运行页面上的调试代码进行分析');
                } else {
                    console.log('⚠️ 未找到菜单项，请确保已登录管理后台');
                }
            }, 2000);
        } else {
            console.log('ℹ️ 请在管理后台页面中进行调试');
        }
    </script>
</body>
</html>
