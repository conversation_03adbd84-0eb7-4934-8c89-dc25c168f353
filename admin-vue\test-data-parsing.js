// 用量数据解析测试
console.log('🧪 开始测试用量数据解析逻辑...');

// 模拟实际API响应数据
const mockApiResponse = {
    "users": [
        {
            "username": "user:001outlookgay",
            "usage": {
                "totalChars": 32102,
                "monthlyChars": 0,
                "monthlyResetAt": 1754006400000
            },
            "createdAt": 1751130017898,
            "vip": {
                "expireAt": 1751170355959,
                "type": "PT"
            }
        },
        {
            "username": "user:1212",
            "usage": {
                "totalChars": 57310,
                "monthlyChars": 57310,
                "monthlyResetAt": 1754006400000
            },
            "createdAt": 1752630176981,
            "vip": {
                "expireAt": 1760422948945,
                "type": "Q"
            }
        },
        {
            "username": "user:1321499711",
            "usage": {
                "totalChars": 267276,
                "monthlyChars": 267276,
                "monthlyResetAt": 1754006400000
            },
            "createdAt": 1751900444277,
            "vip": {
                "expireAt": 1757084554674,
                "type": "PM"
            }
        }
    ],
    "pagination": {
        "limit": 200,
        "hasMore": true,
        "cursor": "AAAAAD3BH715WTyQLnFrZ1j44d3NDsKV1gsAAu6Oz39IdL5s73PFdS9GSW97FsoufYFwVfreg3Yq27qmJXFhNtoJcyDnc-tdYbHcqAFv8ql2i4qa97_XN167eaimCcqdcyzabb524m2ncNVZ9D0ZHxCzISSMYyzBYzU2bfh-aB0nGG9uyraGR_BeYLkXH0l_pHJniDXGX6ilZqNVpdDumO2ZxMtIbGHrpk-riW1ij-s5JXDRcn_iG26IsqZaxabHq2ED5MQUeDvjb95Z4TNIdV2zAEtklQXIjOXdei5FJJXyPhODLuwxV9yZe8uskMfL1CFn143TIap_18xakc9tOnXPCSCE3Ic",
        "total": 93
    },
    "timestamp": 1753004404874
};

// 模拟修复后的解析逻辑
function parseUsageResponse(response) {
    console.log('📥 原始响应:', response);
    
    // 检查响应结构
    if (response.users || (response.success && (response.data?.users || response.users))) {
        const users = response.users || response.data?.users || [];
        const pagination = response.pagination || response.data?.pagination || {};
        
        console.log('✅ 响应结构检查通过');
        console.log('👥 用户数据:', users.length, '条');
        console.log('📄 分页信息:', pagination);
        
        // 计算统计数据
        const stats = calculateUsageStats(users);
        console.log('📊 计算的统计数据:', stats);
        
        return {
            users: users,
            hasMore: pagination.hasMore || false,
            nextCursor: pagination.cursor,
            stats: stats
        };
    }
    
    throw new Error('响应结构不匹配');
}

// 计算用量统计数据
function calculateUsageStats(users) {
    let totalCharsUsed = 0;
    let monthlyCharsUsed = 0;
    let vipUsersCount = 0;
    
    users.forEach(user => {
        totalCharsUsed += user.usage?.totalChars || 0;
        monthlyCharsUsed += user.usage?.monthlyChars || 0;
        
        if (user.vip && user.vip.expireAt > Date.now()) {
            vipUsersCount++;
        }
    });
    
    return {
        totalUsers: users.length,
        totalCharsUsed,
        monthlyCharsUsed,
        vipUsersCount
    };
}

// 执行测试
try {
    console.log('\n🔄 开始解析测试...');
    const result = parseUsageResponse(mockApiResponse);
    
    console.log('\n✅ 解析成功！');
    console.log('📋 解析结果:');
    console.log('  - 用户数量:', result.users.length);
    console.log('  - 是否有更多:', result.hasMore);
    console.log('  - 下一页游标:', result.nextCursor ? '存在' : '不存在');
    console.log('  - 统计数据:');
    console.log('    * 总用户数:', result.stats.totalUsers);
    console.log('    * 历史总字符数:', result.stats.totalCharsUsed.toLocaleString());
    console.log('    * 本月字符数:', result.stats.monthlyCharsUsed.toLocaleString());
    console.log('    * VIP用户数:', result.stats.vipUsersCount);
    
    console.log('\n🎉 测试通过！修复逻辑正确工作');
    
} catch (error) {
    console.error('\n❌ 测试失败:', error.message);
}

// 测试边界情况
console.log('\n🧪 测试边界情况...');

// 测试空响应
try {
    parseUsageResponse({});
    console.log('❌ 空响应测试失败：应该抛出错误');
} catch (error) {
    console.log('✅ 空响应测试通过：正确抛出错误');
}

// 测试带success字段的响应
try {
    const successResponse = {
        success: true,
        data: {
            users: mockApiResponse.users,
            pagination: mockApiResponse.pagination
        }
    };
    const result = parseUsageResponse(successResponse);
    console.log('✅ success字段响应测试通过');
} catch (error) {
    console.log('❌ success字段响应测试失败:', error.message);
}

console.log('\n🏁 所有测试完成！');
