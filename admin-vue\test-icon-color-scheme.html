<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>侧边栏图标变色方案验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        .warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background: #f8f9fa;
            font-weight: 600;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            overflow-x: auto;
        }
        .fix-item {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-left: 4px solid #28a745;
            border-radius: 4px;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 15px 0;
        }
        .before, .after {
            padding: 15px;
            border-radius: 8px;
        }
        .before {
            background: #fff5f5;
            border: 1px solid #fed7d7;
        }
        .after {
            background: #f0fff4;
            border: 1px solid #c6f6d5;
        }
        .sidebar-demo {
            display: flex;
            gap: 30px;
            margin: 20px 0;
            justify-content: center;
        }
        .demo-sidebar {
            width: 64px;
            height: 250px;
            background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .demo-title {
            font-size: 12px;
            color: #6b7280;
            font-weight: 600;
            margin-bottom: 5px;
        }
        .demo-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .demo-icon.normal {
            background: transparent;
            color: #6b7280;
        }
        .demo-icon.selected-bg {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
        .demo-icon.selected-color {
            background: transparent;
            color: #3b82f6;
            font-weight: 600;
            filter: drop-shadow(0 0 4px rgba(59, 130, 246, 0.3));
        }
        .demo-icon.hover {
            background: rgba(59, 130, 246, 0.08);
            color: #3b82f6;
            transform: scale(1.02);
        }
        .demo-icon:hover {
            transform: scale(1.05);
        }
        .scheme-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .scheme-card {
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            border: 2px solid #e2e8f0;
        }
        .scheme-card.recommended {
            border-color: #3b82f6;
            background: linear-gradient(135deg, #eff6ff, #dbeafe);
        }
        .scheme-title {
            font-weight: 600;
            margin-bottom: 15px;
            color: #1f2937;
        }
        .pros-cons {
            text-align: left;
            margin-top: 15px;
        }
        .pros-cons h5 {
            margin: 10px 0 5px 0;
            font-size: 14px;
        }
        .pros-cons ul {
            margin: 0;
            padding-left: 20px;
            font-size: 13px;
        }
        .pros {
            color: #059669;
        }
        .cons {
            color: #dc2626;
        }
    </style>
</head>
<body>
    <h1>🎨 侧边栏图标变色方案验证</h1>
    
    <div class="test-section info">
        <h2>🎯 设计方案对比</h2>
        <div class="scheme-comparison">
            <div class="scheme-card">
                <div class="scheme-title">方案A：背景变色</div>
                <div class="demo-sidebar" style="height: 150px;">
                    <div class="demo-icon normal">👥</div>
                    <div class="demo-icon selected-bg">📊</div>
                    <div class="demo-icon normal">💳</div>
                </div>
                <div class="pros-cons">
                    <h5 class="pros">优点：</h5>
                    <ul class="pros">
                        <li>选中状态明显</li>
                        <li>视觉冲击力强</li>
                    </ul>
                    <h5 class="cons">缺点：</h5>
                    <ul class="cons">
                        <li>收缩时有色块</li>
                        <li>视觉较重</li>
                    </ul>
                </div>
            </div>
            
            <div class="scheme-card recommended">
                <div class="scheme-title">方案B：图标变色 ⭐</div>
                <div class="demo-sidebar" style="height: 150px;">
                    <div class="demo-icon normal">👥</div>
                    <div class="demo-icon selected-color">📊</div>
                    <div class="demo-icon normal">💳</div>
                </div>
                <div class="pros-cons">
                    <h5 class="pros">优点：</h5>
                    <ul class="pros">
                        <li>简洁现代</li>
                        <li>无背景干扰</li>
                        <li>图标突出</li>
                    </ul>
                    <h5 class="cons">缺点：</h5>
                    <ul class="cons">
                        <li>需要适当增强</li>
                    </ul>
                </div>
            </div>
            
            <div class="scheme-card">
                <div class="scheme-title">方案C：混合方案</div>
                <div class="demo-sidebar" style="height: 150px;">
                    <div class="demo-icon normal">👥</div>
                    <div class="demo-icon hover">📊</div>
                    <div class="demo-icon normal">💳</div>
                </div>
                <div class="pros-cons">
                    <h5 class="pros">优点：</h5>
                    <ul class="pros">
                        <li>平衡效果</li>
                        <li>微妙背景</li>
                    </ul>
                    <h5 class="cons">缺点：</h5>
                    <ul class="cons">
                        <li>复杂度高</li>
                        <li>不够统一</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="test-section success">
        <h2>✅ 图标变色方案实现</h2>
        
        <div class="fix-item">
            <h3>1. 展开状态（保持背景色）</h3>
            <div class="code-block">
/* 展开状态选中项 - 保持原有的背景色方案 */
:deep(.n-menu:not(.n-menu--collapsed) .n-menu-item--selected) {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
  color: white !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transform: translateX(2px);
}
            </div>
            <p>✅ 展开状态保持背景色，因为有文字需要对比度</p>
        </div>

        <div class="fix-item">
            <h3>2. 收缩状态（仅图标变色）</h3>
            <div class="code-block">
/* 收缩状态选中项 - 仅图标变色 */
:deep(.n-menu--collapsed .n-menu-item--selected) {
  background: transparent !important;
  color: #3b82f6 !important;
  transform: scale(1.05);
}

/* 收缩状态选中图标增强效果 */
:deep(.n-menu--collapsed .n-menu-item--selected .n-icon) {
  color: #3b82f6 !important;
  font-weight: 600;
  filter: drop-shadow(0 0 4px rgba(59, 130, 246, 0.3));
}
            </div>
            <p>✅ 收缩状态无背景，图标变蓝色并添加阴影效果</p>
        </div>

        <div class="fix-item">
            <h3>3. 悬停效果优化</h3>
            <div class="code-block">
/* 收缩状态悬停 - 微妙的背景和图标变色 */
:deep(.n-menu--collapsed .n-menu-item:hover:not(.n-menu-item--selected)) {
  background: rgba(59, 130, 246, 0.08) !important;
  transform: scale(1.02);
}

:deep(.n-menu--collapsed .n-menu-item:hover:not(.n-menu-item--selected) .n-icon) {
  color: #3b82f6 !important;
  transform: scale(1.1);
}
            </div>
            <p>✅ 悬停时图标变蓝色，背景有微妙的蓝色透明度</p>
        </div>

        <div class="fix-item">
            <h3>4. 选中项悬停增强</h3>
            <div class="code-block">
/* 收缩状态选中项悬停 */
:deep(.n-menu--collapsed .n-menu-item--selected:hover) {
  background: rgba(59, 130, 246, 0.12) !important;
  transform: scale(1.08);
}

:deep(.n-menu--collapsed .n-menu-item--selected:hover .n-icon) {
  color: #2563eb !important;
  transform: scale(1.15);
  filter: drop-shadow(0 0 6px rgba(59, 130, 246, 0.5));
}
            </div>
            <p>✅ 选中项悬停时图标颜色加深，阴影增强</p>
        </div>
    </div>

    <div class="test-section info">
        <h2>📊 方案优势分析</h2>
        <table>
            <tr>
                <th>对比项</th>
                <th>背景变色方案</th>
                <th>图标变色方案</th>
            </tr>
            <tr>
                <td>视觉简洁度</td>
                <td>❌ 有色块，较重</td>
                <td>✅ 无背景，简洁</td>
            </tr>
            <tr>
                <td>现代感</td>
                <td>⚠️ 传统设计</td>
                <td>✅ 现代极简</td>
            </tr>
            <tr>
                <td>图标突出度</td>
                <td>⚠️ 背景可能干扰</td>
                <td>✅ 图标更突出</td>
            </tr>
            <tr>
                <td>收缩状态美观</td>
                <td>❌ 色块突兀</td>
                <td>✅ 和谐统一</td>
            </tr>
            <tr>
                <td>选中识别度</td>
                <td>✅ 非常明显</td>
                <td>✅ 清晰可见</td>
            </tr>
            <tr>
                <td>悬停反馈</td>
                <td>⚠️ 需要处理背景</td>
                <td>✅ 自然流畅</td>
            </tr>
        </table>
    </div>

    <div class="test-section warning">
        <h2>🚀 测试步骤</h2>
        <ol>
            <li><strong>访问管理后台</strong>
                <ul>
                    <li>打开 <a href="http://localhost:5174" target="_blank">http://localhost:5174</a></li>
                    <li>登录管理后台</li>
                </ul>
            </li>
            <li><strong>测试收缩状态图标变色</strong>
                <ul>
                    <li>✅ 侧边栏默认收缩状态</li>
                    <li>✅ 当前页面图标应该是蓝色（#3b82f6）</li>
                    <li>✅ 其他图标应该是灰色（#6b7280）</li>
                    <li>✅ 选中图标有微妙的阴影效果</li>
                    <li>✅ 背景完全透明，无色块</li>
                </ul>
            </li>
            <li><strong>测试悬停效果</strong>
                <ul>
                    <li>✅ 悬停未选中图标变蓝色</li>
                    <li>✅ 悬停有微妙的背景色（rgba(59, 130, 246, 0.08)）</li>
                    <li>✅ 悬停选中图标颜色加深</li>
                    <li>✅ 图标有轻微的缩放动画</li>
                </ul>
            </li>
            <li><strong>测试展开状态</strong>
                <ul>
                    <li>✅ 鼠标悬停侧边栏展开</li>
                    <li>✅ 展开状态保持背景色方案</li>
                    <li>✅ 文字和图标都是白色</li>
                    <li>✅ 背景是蓝色渐变</li>
                </ul>
            </li>
            <li><strong>测试页面切换</strong>
                <ul>
                    <li>✅ 切换到用户管理，图标变蓝色</li>
                    <li>✅ 切换到用量统计，图标变蓝色</li>
                    <li>✅ 切换到卡密管理，图标变蓝色</li>
                    <li>✅ 其他页面图标恢复灰色</li>
                </ul>
            </li>
        </ol>
    </div>

    <div class="test-section success">
        <h2>🎯 预期效果</h2>
        <ul>
            <li><strong>视觉简洁</strong>：收缩状态下无背景色块，更加简洁现代</li>
            <li><strong>图标突出</strong>：选中的图标通过颜色变化清晰标识</li>
            <li><strong>交互自然</strong>：悬停效果微妙而有效</li>
            <li><strong>状态一致</strong>：展开和收缩状态各有最佳的视觉方案</li>
            <li><strong>现代设计</strong>：符合当前UI设计趋势</li>
        </ul>
    </div>

    <script>
        console.log('🎨 图标变色方案验证页面已加载');
        console.log('📝 请按照测试步骤验证新的设计效果');
        
        // 模拟图标状态变化
        function simulateIconStates() {
            console.log('🎨 图标状态演示:');
            console.log('  - 普通状态：灰色图标 (#6b7280)');
            console.log('  - 选中状态：蓝色图标 (#3b82f6) + 阴影');
            console.log('  - 悬停状态：蓝色图标 + 微妙背景');
            console.log('  - 选中悬停：深蓝图标 (#2563eb) + 增强阴影');
        }
        
        // 页面加载时执行演示
        setTimeout(simulateIconStates, 1000);
        
        // 添加交互演示
        document.querySelectorAll('.demo-icon').forEach(icon => {
            icon.addEventListener('mouseenter', function() {
                if (!this.classList.contains('selected-bg') && !this.classList.contains('selected-color')) {
                    this.style.background = 'rgba(59, 130, 246, 0.08)';
                    this.style.color = '#3b82f6';
                }
            });
            
            icon.addEventListener('mouseleave', function() {
                if (!this.classList.contains('selected-bg') && !this.classList.contains('selected-color')) {
                    this.style.background = 'transparent';
                    this.style.color = '#6b7280';
                }
            });
        });
        
        console.log('✅ 交互演示已启用，可以悬停图标查看效果');
    </script>
</body>
</html>
