# 用户使用量数据适配说明

## 🎯 适配目标

适配后端返回的新用户使用量数据格式，支持历史总使用量和当月使用量的展示，并处理用户无使用量时的情况。

## 📊 数据格式分析

### 后端返回的新数据格式

```json
{
  "id": 4,
  "code": "kL8EVm3uO7897RjSBDuAAifRcilHBEVm",
  "package_type": "PM",
  "status": "used",
  "package_info": {
    "type": "PM",
    "price": 45,
    "duration": 2592000000,
    "quotaChars": 250000,
    "description": "PRO月套餐"
  },
  "created_at": "2025-07-25T14:15:41.469Z",
  "used_at": "2025-07-25T14:16:11.328Z",
  "used_by": "0725",
  "userUsage": {                    // ✅ 新增：用户使用量信息
    "totalChars": 2508,             // 历史总使用量
    "monthlyChars": 2508,           // 当月使用量
    "monthlyResetAt": 1754006400000 // 月度重置时间戳
  }
}
```

### 数据特点

1. **条件性存在**：`userUsage` 字段仅在用户有使用记录时存在
2. **完整信息**：包含历史总量、当月使用量、重置时间
3. **数值类型**：所有数值都是number类型
4. **时间戳**：`monthlyResetAt` 是Unix时间戳

## 🔧 技术实现

### 1. 类型定义更新

```typescript
// admin-vue/src/types/index.ts
export interface Card {
  // ... 其他字段
  userUsage?: {                  // 新格式：用户使用量信息 (仅在已使用且有使用记录时存在)
    totalChars: number           // 历史总使用量
    monthlyChars: number         // 当月使用量
    monthlyResetAt: number       // 月度重置时间戳
  }
  // 向后兼容的旧格式
  totalChars?: number            // 字符使用总量 (兼容旧格式)
}
```

### 2. 数据处理逻辑

```typescript
// admin-vue/src/api/cards.ts
return {
  // ... 其他字段
  userUsage: card.userUsage ? {
    totalChars: card.userUsage.totalChars || 0,
    monthlyChars: card.userUsage.monthlyChars || 0,
    monthlyResetAt: card.userUsage.monthlyResetAt || 0
  } : undefined,
  // 兼容旧格式，优先使用新数据
  totalChars: card.userUsage?.totalChars || card.totalChars || 0
} as Card
```

### 3. 表格列配置

#### 历史总使用量列

```typescript
{
  key: 'totalChars',
  title: '历史总使用量',
  width: 150,
  minWidth: 120,
  render: (row: Card) => {
    if (row.status === 'unused') {
      return h('span', { style: 'color: #9ca3af;' }, '-')
    }
    
    // 优先使用新格式的userUsage数据
    const totalChars = row.userUsage?.totalChars ?? row.totalChars
    
    if (totalChars !== undefined && totalChars > 0) {
      return h('span', {
        style: 'color: #1f2937; font-weight: 500;'
      }, totalChars.toLocaleString())
    }
    
    // 如果已使用但没有使用量数据，显示"无数据"
    return h('span', { style: 'color: #f59e0b;' }, '无数据')
  }
}
```

#### 当月使用量列

```typescript
{
  key: 'monthlyChars',
  title: '当月使用量',
  width: 140,
  minWidth: 120,
  render: (row: Card) => {
    if (row.status === 'unused') {
      return h('span', { style: 'color: #9ca3af;' }, '-')
    }
    
    const monthlyChars = row.userUsage?.monthlyChars
    
    if (monthlyChars !== undefined && monthlyChars > 0) {
      return h('span', {
        style: 'color: #059669; font-weight: 500;'
      }, monthlyChars.toLocaleString())
    }
    
    if (row.userUsage) {
      // 有userUsage但monthlyChars为0，说明本月未使用
      return h('span', { style: 'color: #9ca3af;' }, '0')
    }
    
    // 没有userUsage数据
    return h('span', { style: 'color: #f59e0b;' }, '无数据')
  }
}
```

## 🎨 UI展示策略

### 状态颜色编码

| 状态 | 颜色 | 说明 |
|------|------|------|
| 未使用 | `#9ca3af` (灰色) | 卡密未激活 |
| 有使用量 | `#1f2937` (深灰) / `#059669` (绿色) | 正常使用数据 |
| 无数据 | `#f59e0b` (橙色) | 已使用但无使用量记录 |
| 零使用 | `#9ca3af` (灰色) | 本月未使用 |

### 数据格式化

- **数值格式化**：使用 `toLocaleString()` 添加千分位分隔符
- **状态区分**：不同状态使用不同颜色和文本
- **空值处理**：统一显示 `-` 或 `无数据`

## 🔄 数据流程

### 1. 后端响应处理

```
后端响应 → API层标准化 → Store状态管理 → 组件渲染
```

### 2. 数据优先级

```
userUsage.totalChars > totalChars > 0 (默认值)
userUsage.monthlyChars > 0 (默认值)
```

### 3. 错误处理

- **网络错误**：显示加载失败提示
- **数据缺失**：显示"无数据"状态
- **格式错误**：使用默认值或回退机制

## 📋 兼容性处理

### 向后兼容

- 保留 `totalChars` 字段支持
- 优先使用新格式数据
- 自动回退到旧格式

### 渐进式升级

- 新旧数据格式并存
- 逐步迁移到新格式
- 保持功能稳定性

## 🧪 测试场景

### 1. 数据存在场景

- ✅ 有完整userUsage数据
- ✅ totalChars > 0, monthlyChars > 0
- ✅ 正确显示格式化数值

### 2. 数据缺失场景

- ✅ 无userUsage字段
- ✅ userUsage存在但字段为0
- ✅ 显示"无数据"或"0"

### 3. 状态场景

- ✅ 未使用卡密显示"-"
- ✅ 已使用卡密显示使用量
- ✅ 颜色编码正确

### 4. 兼容性场景

- ✅ 旧格式数据正常显示
- ✅ 新旧格式混合正常工作
- ✅ 数据优先级正确

## 📊 表格布局优化

### 列宽调整

| 列名 | 宽度 | 最小宽度 | 说明 |
|------|------|----------|------|
| 历史总使用量 | 150px | 120px | 显示完整数值 |
| 当月使用量 | 140px | 120px | 突出当月数据 |
| 总滚动宽度 | 1600px | - | 适应新增列 |

### 响应式设计

- 小屏幕自动隐藏次要列
- 保持核心信息可见
- 横向滚动支持

## 🎯 用户体验

### 信息层次

1. **主要信息**：历史总使用量
2. **次要信息**：当月使用量
3. **状态信息**：使用状态和时间

### 视觉反馈

- **数值突出**：使用粗体和深色
- **状态清晰**：颜色编码区分
- **加载状态**：显示加载指示器

## 🚀 性能优化

### 数据处理

- 在API层进行数据标准化
- 避免在渲染时重复计算
- 使用计算属性缓存结果

### 渲染优化

- 虚拟滚动支持大量数据
- 按需渲染减少DOM操作
- 合理的分页大小

---

**适配时间：** 2025-01-26  
**适配类型：** 数据格式适配 + UI增强  
**适配状态：** ✅ 已完成  
**测试状态：** 🧪 待验证

## 🔍 验证清单

- [ ] 新数据格式正确解析
- [ ] 表格列正常显示
- [ ] 颜色编码正确
- [ ] 数值格式化正确
- [ ] 兼容性正常
- [ ] 错误处理正确
