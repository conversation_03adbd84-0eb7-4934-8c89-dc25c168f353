# 用量统计API修复说明

## 🔍 问题分析

### 1. **URL域名不匹配**
- **用户实际请求**: `https://api.myaitts.com/api/admin/users/usage?limit=200`
- **前端配置的URL**: `https://cardapi.aispeak.top/api/admin/users/usage`
- **问题**: 域名不一致导致请求失败

### 2. **重复请求问题**
从截图看到多次相同的请求：
```
usage?limit=200
usage?limit=1000  
usage?limit=1000
```
说明存在重复调用的逻辑问题。

### 3. **API端点配置**
根据文档，正确的用量统计接口应该是：
- **接口地址**: `GET /api/admin/users/usage`
- **查询参数**: `limit` (默认100，最大1000), `cursor` (分页游标)
- **权限要求**: 需要管理员权限

## 🔧 修复方案

### 1. 更新API基础URL配置

```typescript
// admin-vue/src/api/config.ts
export const API_CONFIG = {
  // 更新为正确的API地址
  BASE_URL: import.meta.env.VITE_API_BASE_URL || 'https://api.myaitts.com',
  TIMEOUT: parseInt(import.meta.env.VITE_API_TIMEOUT || '10000'),
  RETRY_COUNT: parseInt(import.meta.env.VITE_API_RETRY_COUNT || '3')
}
```

### 2. 修复旧版HTML中的硬编码URL

```javascript
// src/index.html (第2620行)
// 修复前
let url = `https://cardapi.aispeak.top/api/admin/users/usage?limit=${limit}`;

// 修复后  
let url = `https://api.myaitts.com/api/admin/users/usage?limit=${limit}`;
```

### 3. 优化用量统计API调用逻辑

```typescript
// admin-vue/src/api/users.ts
static async getUsageData(params: PaginationParams = {}): Promise<{
  users: UsageData[]
  hasMore: boolean
  nextCursor?: string
  stats: StatsData
}> {
  try {
    const queryParams = new URLSearchParams()

    // 设置默认limit为100，最大1000
    const limit = Math.min(params.limit || 100, 1000)
    queryParams.append('limit', limit.toString())

    if (params.cursor) {
      queryParams.append('cursor', params.cursor)
    }

    const url = `${API_ENDPOINTS.USAGE}${queryParams.toString() ? '?' + queryParams.toString() : ''}`
    console.log('请求用量统计API:', url)
    console.log('请求参数:', params)
    
    const response = await httpClient.get<any>(url)
    console.log('用量统计API响应:', response)

    // 处理响应数据...
  } catch (error) {
    throw new Error(error instanceof Error ? error.message : '获取用量数据失败')
  }
}
```

### 4. 防止重复请求的优化

```typescript
// admin-vue/src/views/admin/UsageView.vue
// 组件激活时不自动加载数据，改为手动刷新模式
onActivated(() => {
  // 使用缓存数据，需要时请手动刷新
})

// 处理页面大小变化时避免重复请求
const handlePageSizeChange = (value: number) => {
  pageSize.value = value
  handleRefresh() // 统一通过refresh处理
}
```

## 🎯 修复效果

### 修复前的问题
- ❌ 请求错误的域名 `cardapi.aispeak.top`
- ❌ 多次重复请求相同的接口
- ❌ 硬编码的URL难以维护
- ❌ 缺少详细的调试信息

### 修复后的改进
- ✅ 使用正确的API域名 `api.myaitts.com`
- ✅ 统一的API配置管理
- ✅ 防止重复请求的逻辑优化
- ✅ 详细的调试日志输出
- ✅ 支持环境变量配置
- ✅ 合理的默认参数设置

## 📋 API接口规范

### 请求格式
```bash
GET https://api.myaitts.com/api/admin/users/usage?limit=100&cursor=xxx
Authorization: Bearer YOUR_ADMIN_TOKEN
```

### 查询参数
| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `limit` | number | 否 | 100 | 每页返回的用户数量，最大1000 |
| `cursor` | string | 否 | null | 分页游标，用于获取下一页数据 |

### 响应格式
```json
{
  "users": [
    {
      "username": "user1",
      "usage": {
        "totalChars": 1250,
        "monthlyChars": 450,
        "monthlyResetAt": 1735689600000
      },
      "createdAt": 1703123456789,
      "vip": {
        "expireAt": 1735689600000,
        "type": "S"
      }
    }
  ],
  "pagination": {
    "limit": 100,
    "hasMore": true,
    "cursor": "eyJrZXkiOiJ1c2VyOnVzZXIxMDAifQ==",
    "total": 2
  },
  "timestamp": 1703123456789
}
```

## 🔍 调试信息

修复后的代码会输出详细的调试信息：

```javascript
console.log('请求用量统计API:', url)
console.log('请求参数:', params)
console.log('用量统计API响应:', response)
```

## 🧪 测试验证

### 1. URL正确性测试
- [ ] 验证请求URL使用正确的域名
- [ ] 确认API端点路径正确
- [ ] 检查查询参数格式

### 2. 权限验证测试
- [ ] 使用管理员账号测试
- [ ] 验证Token正确传递
- [ ] 确认权限检查通过

### 3. 分页功能测试
- [ ] 测试默认分页参数
- [ ] 验证cursor分页机制
- [ ] 测试limit参数限制

### 4. 重复请求检查
- [ ] 监控网络请求次数
- [ ] 验证不会产生重复请求
- [ ] 确认缓存机制正常

## 📝 注意事项

### 1. 环境变量配置
```bash
# .env 文件
VITE_API_BASE_URL=https://api.myaitts.com
VITE_API_TIMEOUT=10000
VITE_API_RETRY_COUNT=3
```

### 2. 权限要求
- 用户必须在 `ADMIN_USERS` 环境变量中配置
- 需要有效的JWT Token
- Token必须包含正确的用户名信息

### 3. 性能优化
- 默认每页100条记录，避免一次性加载过多数据
- 使用游标分页，支持大数据量查询
- 实现请求去重，避免重复调用

### 4. 错误处理
- 详细的错误信息提示
- 权限错误的友好提示
- 网络错误的重试机制

---

**修复时间：** 2025-01-26  
**问题类型：** API URL配置错误  
**修复状态：** ✅ 已完成  
**测试状态：** 🧪 待验证

## 🚀 下一步操作

1. **重新部署前端应用**
2. **测试用量统计功能**
3. **验证API请求正确性**
4. **检查调试日志输出**
