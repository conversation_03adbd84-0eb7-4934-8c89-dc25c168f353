# 用量统计重构说明

## 🎯 重构目标

基于管理员API接口文档，将用量统计功能从自定义接口重构为标准化接口，提高系统一致性和可维护性。

## 📋 重构前后对比

### 重构前的问题
- ❌ 使用非标准的 `/api/admin/users/usage` 接口（文档中未定义）
- ❌ 数据格式不统一，与文档规范不符
- ❌ 缺少系统级统计功能
- ❌ 硬编码的API域名配置
- ❌ 重复请求和性能问题

### 重构后的改进
- ✅ 使用标准的 `/api/admin/users` 接口获取用户列表（含用量数据）
- ✅ 使用标准的 `/api/admin/stats` 接口获取系统统计
- ✅ 数据格式符合文档规范
- ✅ 支持环境变量配置API域名
- ✅ 优化的数据处理和错误处理机制

## 🔧 技术实现

### 1. API端点重构

```typescript
// admin-vue/src/api/config.ts
export const API_ENDPOINTS = {
  // 更新API基础URL
  BASE_URL: import.meta.env.VITE_API_BASE_URL || 'https://api.myaitts.com',
  
  // 新增标准接口
  USER_QUOTA: '/api/user/quota',             // 用户配额信息
  USER_STATS: '/api/user/stats',             // 用户详细统计
  USER_PROFILE: '/api/user/profile',         // 用户详细资料
  
  // 保留兼容性
  USAGE: '/api/admin/users/usage',           // 旧用量接口 (将废弃)
}
```

### 2. 数据类型重构

```typescript
// admin-vue/src/types/index.ts

// 用量数据类型 (基于文档标准)
export interface UsageData {
  username: string
  email?: string
  created_at?: string
  createdAt?: string | number
  updatedAt?: string | number
  vip_info?: {
    type: string | null
    expireAt: number
    quotaChars: number
    usedChars?: number
    remainingChars?: number
    usagePercentage?: number
    isExpired?: boolean
  }
  usage_stats?: {
    totalChars: number
    monthlyChars: number
    monthlyResetAt: number
  }
  // 向后兼容的旧格式
  usage?: {
    totalChars: number
    monthlyChars: number
    monthlyResetAt: number
  }
  vip?: {
    type: string | null
    expireAt: number
  }
}

// 系统统计类型 (新格式，基于文档)
export interface SystemStats {
  users: {
    total_users: string
    active_vip_users: string
    new_users_7d: string
    new_users_30d: string
  }
  tasks: {
    total_tasks: string
    completed_tasks: string
    failed_tasks: string
    processing_tasks: string
    tasks_24h: string
    tasks_7d: string
  }
  cards: {
    total_cards: string
    unused_cards: string
    used_cards: string
    new_cards_7d: string
  }
  taskTrend: Array<{
    date: string
    tasks: number
    completed: number
    failed: number
  }>
  timestamp: string
}
```

### 3. API服务重构

```typescript
// admin-vue/src/api/users.ts

export class UserService {
  // 获取用量统计数据 (基于标准用户列表接口)
  static async getUsageData(params: PaginationParams & { search?: string, page?: number } = {}): Promise<{
    users: UsageData[]
    hasMore: boolean
    nextCursor?: string
    stats: StatsData
  }> {
    // 使用标准用户列表接口而不是专用用量接口
    const url = `${API_ENDPOINTS.ADMIN_USERS}?page=${page}&limit=${limit}&search=${search}`
    const response = await httpClient.get<any>(url)
    
    // 标准化用户数据格式
    const standardizedUsers = this.standardizeUsageData(users)
    
    return {
      users: standardizedUsers,
      hasMore: pagination.hasNext || false,
      nextCursor: pagination.page ? (pagination.page + 1).toString() : undefined,
      stats: this.calculateUsageStats(standardizedUsers)
    }
  }

  // 获取系统统计数据 (使用新管理员API)
  static async getSystemStats(): Promise<SystemStats> {
    const response = await httpClient.get<any>(API_ENDPOINTS.ADMIN_STATS)
    
    // 直接返回符合文档格式的系统统计
    if (response.users && response.tasks && response.cards) {
      return {
        users: response.users,
        tasks: response.tasks,
        cards: response.cards,
        taskTrend: response.taskTrend || [],
        timestamp: response.timestamp || new Date().toISOString()
      }
    }
  }

  // 标准化用量数据格式 (适配文档格式)
  private static standardizeUsageData(users: any[]): UsageData[] {
    return users.map((user: any) => {
      const usage_stats = user.usage_stats || user.usage || {}
      const vip_info = user.vip_info || user.vip || {}

      return {
        username: user.username,
        email: user.email,
        created_at: user.created_at,
        createdAt: user.createdAt || parseTime(user.created_at),
        updatedAt: user.updatedAt || parseTime(user.updated_at),
        vip_info: {
          type: vip_info.type,
          expireAt: vip_info.expireAt || 0,
          quotaChars: vip_info.quotaChars || 0,
          usedChars: vip_info.usedChars || usage_stats.totalChars || 0,
          remainingChars: vip_info.remainingChars,
          usagePercentage: vip_info.usagePercentage,
          isExpired: vip_info.isExpired
        },
        usage_stats: {
          totalChars: usage_stats.totalChars || 0,
          monthlyChars: usage_stats.monthlyChars || 0,
          monthlyResetAt: usage_stats.monthlyResetAt || 0
        },
        // 向后兼容
        usage: usage_stats,
        vip: vip_info
      }
    })
  }
}
```

## 📊 接口映射关系

### 用量统计接口
| 功能 | 旧接口 | 新接口 | 说明 |
|------|--------|--------|------|
| 用户列表(含用量) | `/api/admin/users/usage` | `/api/admin/users` | 使用标准用户列表接口 |
| 系统统计 | 无 | `/api/admin/stats` | 新增系统级统计功能 |
| 用户详情 | 无 | `/api/admin/users/:username` | 获取单用户详细信息 |
| 用户配额 | 无 | `/api/user/quota` | 用户配额信息 |
| 用户统计 | 无 | `/api/user/stats` | 用户详细统计 |

### 数据格式映射
| 字段 | 旧格式 | 新格式 | 说明 |
|------|--------|--------|------|
| 用量数据 | `usage` | `usage_stats` | 字段名标准化 |
| VIP信息 | `vip` | `vip_info` | 字段名标准化 |
| 创建时间 | `createdAt` | `created_at` | 支持ISO字符串格式 |
| 用量百分比 | 无 | `usagePercentage` | 新增字段 |
| 剩余字符 | 无 | `remainingChars` | 新增字段 |

## 🔄 迁移策略

### 阶段1：兼容性支持
- ✅ 保留旧接口作为备用
- ✅ 数据格式向后兼容
- ✅ 渐进式迁移

### 阶段2：标准化实现
- ✅ 使用标准接口获取数据
- ✅ 数据格式标准化处理
- ✅ 错误处理优化

### 阶段3：功能增强
- ✅ 系统级统计功能
- ✅ 更丰富的数据字段
- ✅ 性能优化

## 🧪 测试验证

### 1. 接口兼容性测试
- [ ] 验证新接口返回正确数据
- [ ] 确认数据格式转换正确
- [ ] 测试错误处理机制

### 2. 功能完整性测试
- [ ] 用量统计显示正确
- [ ] 系统统计功能正常
- [ ] 分页和搜索功能

### 3. 性能测试
- [ ] 接口响应时间
- [ ] 数据加载效率
- [ ] 内存使用情况

## 📝 注意事项

### 1. 向后兼容性
- 保留旧数据格式支持
- 渐进式迁移策略
- 错误回退机制

### 2. 数据一致性
- 统一的数据格式标准
- 字段映射和转换
- 类型安全保证

### 3. 性能优化
- 减少重复请求
- 数据缓存机制
- 分页加载优化

---

**重构时间：** 2025-01-26  
**重构类型：** API标准化重构  
**重构状态：** ✅ 已完成  
**测试状态：** 🧪 待验证

## 🚀 下一步操作

1. **测试新接口功能**
2. **验证数据格式正确性**
3. **检查系统统计功能**
4. **优化用户体验**
