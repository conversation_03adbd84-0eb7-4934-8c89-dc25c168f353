<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理系统</title>
    <link rel="icon" href="https://img.icons8.com/arcade/64/donation.png" type="image/png">
    <link href="css/fonts.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #667eea;
            --primary-hover: #764ba2;
            --secondary-color: #f093fb;
            --accent-color: #4facfe;
            --success-color: #00d4aa;
            --warning-color: #feca57;
            --error-color: #ff6b6b;
            --bg-color: #f8fafc;
            --bg-secondary: #ffffff;
            --text-color: #2d3748;
            --text-secondary: #4a5568;
            --text-light: #718096;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --radius-sm: 6px;
            --radius: 12px;
            --radius-lg: 16px;
            --header-height: 80px;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "PingFang SC", "Microsoft YaHei", "Segoe UI", Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
            color: var(--text-color);
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* 现代化标题样式 */
        h1 {
            text-align: center;
            color: var(--text-color);
            margin-bottom: 3rem;
            font-weight: 700;
            font-size: 2.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            letter-spacing: -0.02em;
        }

        /* 现代化导航标签样式 */
        .nav-tabs {
            display: flex;
            background: var(--bg-secondary);
            padding: 0.75rem;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            margin-bottom: 3rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .nav-tab {
            padding: 1rem 2.5rem;
            cursor: pointer;
            border-radius: var(--radius);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 600;
            font-size: 1rem;
            color: var(--text-secondary);
            position: relative;
            overflow: hidden;
        }

        .nav-tab::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: -1;
        }

        .nav-tab:hover {
            color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .nav-tab.active {
            color: white;
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .nav-tab.active::before {
            opacity: 1;
        }

        /* 现代化表格样式 */
        .table-container {
            background: var(--bg-secondary);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .user-table, .card-table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
        }

        .user-table th,
        .user-table td,
        .card-table th,
        .card-table td {
            padding: 1.25rem 1.5rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
            font-size: 0.95rem;
        }

        .user-table th,
        .card-table th {
            background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
            color: var(--text-color);
            font-weight: 600;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            position: sticky;
            top: 0;
            z-index: 10;
            border-bottom: 2px solid var(--border-color);
        }

        .user-table tr:hover,
        .card-table tr:hover {
            background: linear-gradient(90deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
            transform: scale(1.01);
            transition: all 0.2s ease;
        }

        .user-table tr:last-child td,
        .card-table tr:last-child td {
            border-bottom: none;
        }

        .user-table tbody tr,
        .card-table tbody tr {
            transition: all 0.2s ease;
            cursor: pointer;
        }

        /* 现代化状态标签样式 */
        .status-tag {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.85rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .vip-tag {
            background: linear-gradient(135deg, var(--success-color), #48bb78);
            color: white;
            box-shadow: 0 4px 12px rgba(0, 212, 170, 0.3);
        }

        .non-vip-tag {
            background: linear-gradient(135deg, var(--text-light), #a0aec0);
            color: white;
            box-shadow: 0 4px 12px rgba(113, 128, 150, 0.3);
        }

        .status-unused {
            background: linear-gradient(135deg, var(--success-color), #48bb78);
            color: white;
            box-shadow: 0 4px 12px rgba(0, 212, 170, 0.3);
        }

        /* 用量统计样式 */
        .usage-container {
            padding: 1.5rem;
        }

        .usage-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .usage-header h2 {
            margin: 0;
            color: var(--text-color);
            font-size: 1.8rem;
            font-weight: 600;
        }

        .usage-controls {
            display: flex;
            align-items: center;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .page-info {
            color: var(--text-secondary);
            font-weight: 500;
            min-width: 80px;
            text-align: center;
        }

        .usage-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stats-card {
            background: linear-gradient(135deg, var(--bg-secondary), #f8fafc);
            border-radius: var(--radius-lg);
            padding: 2rem;
            text-align: center;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }

        .stats-card:hover::before {
            opacity: 1;
        }

        .stats-value {
            font-size: 2.2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
            position: relative;
            z-index: 1;
        }

        .stats-label {
            color: var(--text-secondary);
            font-size: 0.95rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            position: relative;
            z-index: 1;
        }

        .usage-char-bar {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .char-progress {
            flex: 1;
            height: 8px;
            background: var(--border-color);
            border-radius: 4px;
            overflow: hidden;
        }

        .char-progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--success-color), var(--accent-color));
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .char-percentage {
            font-size: 0.85rem;
            color: var(--text-secondary);
            font-weight: 600;
            min-width: 45px;
            text-align: right;
        }

        /* 用量表格特殊样式 */
        .usage-high {
            background: linear-gradient(90deg, rgba(255, 107, 107, 0.1), rgba(255, 107, 107, 0.05));
        }

        .usage-medium {
            background: linear-gradient(90deg, rgba(254, 202, 87, 0.1), rgba(254, 202, 87, 0.05));
        }

        .usage-low {
            background: linear-gradient(90deg, rgba(0, 212, 170, 0.1), rgba(0, 212, 170, 0.05));
        }

        .char-count {
            font-weight: 600;
            font-family: 'Monaco', 'Menlo', monospace;
        }

        .char-count.high {
            color: var(--error-color);
        }

        .char-count.medium {
            color: var(--warning-color);
        }

        .char-count.low {
            color: var(--success-color);
        }

        /* 筛选按钮样式 */
        .filter-controls {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .filter-button {
            padding: 0.6rem 1.2rem;
            border: 2px solid var(--border-color);
            border-radius: var(--radius);
            background: var(--bg-secondary);
            color: var(--text-secondary);
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .filter-button:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
            transform: translateY(-1px);
            box-shadow: var(--shadow-sm);
        }

        .filter-button.active {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-color: var(--primary-color);
            box-shadow: var(--shadow-md);
        }

        .filter-button.active:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .status-used {
            background: linear-gradient(135deg, var(--error-color), #f56565);
            color: white;
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
        }

        /* 加载动画 */
        .loading {
            display: none;
            text-align: center;
            padding: 2rem;
        }

        .loading::after {
            content: "加载中...";
            color: var(--primary-color);
        }

        /* 现代化登录界面样式 */
        .login-container {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        .login-box {
            background: var(--bg-secondary);
            padding: 3rem;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            width: 100%;
            max-width: 450px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .login-box h2 {
            margin: 0 0 2.5rem;
            text-align: center;
            color: var(--text-color);
            font-weight: 700;
            font-size: 2rem;
            letter-spacing: -0.02em;
        }

        .auth-input {
            width: 100%;
            padding: 0.8rem 1rem;
            margin-bottom: 1rem;
            border: 2px solid var(--border-color);
            border-radius: var(--radius);
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .auth-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
        }

        button {
            width: 100%;
            padding: 1rem 2rem;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            border-radius: var(--radius);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            box-shadow: var(--shadow-md);
            position: relative;
            overflow: hidden;
        }

        button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        button:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-lg);
        }

        button:hover::before {
            left: 100%;
        }

        button:active {
            transform: translateY(-1px);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .nav-tab {
                padding: 0.6rem 1rem;
            }

            .user-table th,
            .user-table td {
                padding: 0.8rem;
            }

            .table-container {
                overflow-x: auto;
            }
        }

        /* 现代化卡密管理模块样式 */
        .card-container {
            background: var(--bg-secondary);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .card-form {
            max-width: 500px;
            margin: 0 auto;
        }

        .form-select {
            width: 100%;
            padding: 1rem 1.5rem;
            border: 2px solid var(--border-color);
            border-radius: var(--radius);
            font-size: 1rem;
            font-weight: 500;
            background: var(--bg-secondary);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            margin-bottom: 2rem;
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='%23667eea'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 1rem center;
            box-shadow: var(--shadow-sm);
            color: var(--text-color);
        }

        .form-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
        }

        .form-input {
            width: calc(100% - 3rem);
            padding: 1rem 1.5rem;
            border: 2px solid var(--border-color);
            border-radius: var(--radius);
            font-size: 1rem;
            font-weight: 500;
            margin-bottom: 2rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: var(--bg-secondary);
            color: var(--text-color);
            box-shadow: var(--shadow-sm);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
        }

        .preview {
            background-color: var(--bg-color);
            padding: 1.5rem;
            border-radius: var(--radius);
            margin: 1.5rem 0;
        }

        .preview h3 {
            margin-top: 0;
            margin-bottom: 1rem;
            color: #2c3e50;
        }

        .button-group {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .primary-button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: var(--radius);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .primary-button:hover {
            background-color: var(--primary-hover);
            transform: translateY(-1px);
        }

        .secondary-button {
            background-color: #e0e0e0;
            color: #333;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: var(--radius);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .secondary-button:hover {
            background-color: #d0d0d0;
            transform: translateY(-1px);
        }

        .result-card {
            margin-top: 2rem;
            padding: 1rem;
            border-radius: var(--radius);
            display: none;
            text-align: center;
            background-color: #e8f5e9;
            color: var(--primary-color);
            font-weight: 500;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .card-container {
                padding: 1rem;
            }

            .button-group {
                flex-direction: column;
            }

            .button-group button {
                width: 100%;
            }

            .generated-card {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.75rem;
            }

            .card-info {
                width: 100%;
            }

            .copy-btn {
                align-self: flex-end;
                min-width: 80px;
            }

            .result-actions {
                flex-direction: column;
                gap: 0.75rem;
            }

            .result-actions .secondary-button,
            .copy-format-btn {
                width: 100%;
                min-width: auto;
            }

            .copy-format-dropdown {
                position: static;
                margin-top: 0.5rem;
                box-shadow: none;
                border: 1px solid var(--border-color);
            }
        }

        .preview-item {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: var(--radius);
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .preview-item strong {
            color: var(--primary-color);
            margin-right: 10px;
        }

        #result {
            font-size: 1.1em;
            padding: 1.5rem;
        }

        #result.success {
            background-color: #e8f5e9;
            color: #2e7d32;
        }

        #result.error {
            background-color: #ffebee;
            color: #c62828;
        }

        .card-edit-actions {
            margin-top: 1rem;
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        .card-edit-form {
            margin-top: 1rem;
        }

        .form-input {
            width: 92%;
            padding: 1rem 1rem;
            border: 2px solid var(--border-color);
            border-radius: var(--radius);
            font-size: 1rem;
            font-weight: bold;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
        }

        .preview-edit {
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border-color);
        }

        .preview-buttons {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .sortable {
            cursor: pointer;
            user-select: none;
            position: relative;
            padding-right: 20px !important;
        }

        .sortable:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

        .sort-icon {
            position: absolute;
            right: 8px;
            opacity: 0.3;
        }

        .sort-asc .sort-icon {
            opacity: 1;
            content: "↑";
        }

        .sort-desc .sort-icon {
            opacity: 1;
            content: "↓";
        }

        th.active-sort {
            color: var(--primary-color);
            background-color: rgba(76, 175, 80, 0.05);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.75rem;
            color: var(--text-color);
            font-weight: 600;
            font-size: 1.1rem;
            letter-spacing: 0.02em;
        }

        .form-text {
            display: block;
            margin-top: 0.25rem;
            color: #6c757d;
            font-size: 0.875rem;
        }

        .generation-progress {
            margin-top: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: var(--radius);
        }

        .progress-text {
            text-align: center;
            margin-bottom: 0.5rem;
            color: #2c3e50;
        }

        .progress-bar {
            height: 4px;
            background: #e9ecef;
            border-radius: 2px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: var(--primary-color);
            width: 0;
            transition: width 0.3s ease;
        }

        .card-result-content {
            max-height: 300px;
            overflow-y: auto;
            margin-bottom: 1rem;
        }

        .generated-card {
            padding: 0.5rem;
            margin: 0.5rem 0;
            background: #f8f9fa;
            border-radius: var(--radius);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .generated-card:nth-child(even) {
            background: #fff;
        }

        /* 复制按钮样式 */
        .copy-btn {
            background: linear-gradient(135deg, var(--accent-color), #00d4aa);
            color: white;
            border: none;
            padding: 0.4rem 0.8rem;
            border-radius: var(--radius-sm);
            font-size: 0.85rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            box-shadow: 0 2px 4px rgba(79, 172, 254, 0.3);
            position: relative;
            overflow: hidden;
        }

        .copy-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .copy-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(79, 172, 254, 0.4);
        }

        .copy-btn:hover::before {
            left: 100%;
        }

        .copy-btn:active {
            transform: translateY(0);
        }

        .copy-btn.copied {
            background: linear-gradient(135deg, var(--success-color), #48bb78);
            box-shadow: 0 2px 4px rgba(0, 212, 170, 0.3);
        }

        /* 卡密信息区域样式 */
        .card-info {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
            flex: 1;
        }

        .card-code {
            font-family: 'Monaco', 'Menlo', monospace;
            font-weight: 700;
            font-size: 1rem;
            color: var(--text-color);
            letter-spacing: 0.5px;
        }

        .card-type {
            font-size: 0.85rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        /* 结果操作按钮容器 */
        .result-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 1.5rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border-color);
            flex-wrap: wrap;
        }

        .result-actions .secondary-button {
            padding: 0.6rem 1.2rem;
            font-size: 0.9rem;
            min-width: 120px;
        }

        /* 复制格式选择器 */
        .copy-format-selector {
            position: relative;
            display: inline-block;
        }

        .copy-format-btn {
            background: linear-gradient(135deg, var(--text-light), #a0aec0);
            color: white;
            border: none;
            padding: 0.6rem 1.2rem;
            border-radius: var(--radius);
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            box-shadow: var(--shadow-sm);
            min-width: 120px;
            position: relative;
        }

        .copy-format-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
            background: linear-gradient(135deg, #4a5568, #718096);
        }

        .copy-format-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            box-shadow: var(--shadow-lg);
            z-index: 100;
            display: none;
            margin-top: 0.5rem;
        }

        .copy-format-dropdown.show {
            display: block;
        }

        .copy-format-option {
            padding: 0.75rem 1rem;
            cursor: pointer;
            transition: background-color 0.2s ease;
            border-bottom: 1px solid var(--border-color);
            font-size: 0.9rem;
        }

        .copy-format-option:last-child {
            border-bottom: none;
        }

        .copy-format-option:hover {
            background: linear-gradient(90deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
        }

        .copy-format-option-title {
            font-weight: 600;
            color: var(--text-color);
        }

        .copy-format-option-desc {
            font-size: 0.8rem;
            color: var(--text-secondary);
            margin-top: 0.25rem;
        }

        .preview-card {
            background: white;
            border-radius: var(--radius);
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
        }

        .preview-card-header {
            background: var(--primary-color);
            color: white;
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }

        .preview-card-content {
            padding: 1rem;
        }

        .preview-card-content > div {
            margin-bottom: 0.5rem;
        }

        .preview-item {
            margin-bottom: 1rem;
        }

        .preview-item:last-child {
            margin-bottom: 0;
        }

        .preview-card-input {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            margin-top: 0.5rem;
            font-size: 1rem;
        }

        .preview-card-input:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 2px rgba(68, 130, 182, 0.2);
        }

        .preview-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 1rem;
        }

        .tabs-inner {
            background: var(--bg-secondary);
            border-radius: var(--radius-lg);
            padding: 2.5rem;
            box-shadow: var(--shadow-lg);
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .sub-nav-tabs {
            margin-bottom: 2.5rem;
            display: flex;
            gap: 1rem;
            padding: 0.5rem;
            background: var(--bg-color);
            border-radius: var(--radius);
            border: none;
        }

        .card-table {
            width: 100%;
            border-collapse: collapse;
        }

        .card-table th,
        .card-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .card-table tr:hover {
            background-color: rgba(0, 0, 0, 0.02);
        }

        .status-tag {
            padding: 0.25rem 0.5rem;
            border-radius: 1rem;
            font-size: 0.875rem;
        }

        .status-unused {
            background-color: #e8f5e9;
            color: #2e7d32;
        }

        .status-used {
            background-color: #ffebee;
            color: #c62828;
        }

        .card-actions {
            margin-top: 2rem;
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .primary-button, .secondary-button {
            padding: 0.75rem 2rem;
            border-radius: var(--radius);
            font-weight: 600;
            font-size: 0.95rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            border: none;
            position: relative;
            overflow: hidden;
        }

        .primary-button {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: var(--shadow-md);
        }

        .primary-button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .secondary-button {
            background: linear-gradient(135deg, var(--text-light), #a0aec0);
            color: white;
            box-shadow: var(--shadow-sm);
        }

        .secondary-button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
            background: linear-gradient(135deg, #4a5568, #718096);
        }

        .card-table th:first-child,
        .card-table td:first-child {
            width: 40px;
            text-align: center;
        }

        .checkbox-cell {
            text-align: center;
        }

        /* 子标签页样式 */
        .sub-tab-content {
            display: none;
        }

        .sub-tab-content.active {
            display: block;
        }

        /* 搜索框样式 */
        .search-container {
            margin-bottom: 2rem;
        }

        .search-box {
            position: relative;
            max-width: 500px;
            margin: 0 auto;
        }

        .search-input {
            width: 100%;
            padding: 1rem 1.5rem 1rem 3rem;
            border: 2px solid var(--border-color);
            border-radius: var(--radius-lg);
            font-size: 1rem;
            font-weight: 500;
            background: var(--bg-secondary);
            color: var(--text-color);
            box-shadow: var(--shadow-sm);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
        }

        .search-input::placeholder {
            color: var(--text-light);
        }

        .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.2rem;
            color: var(--text-light);
            pointer-events: none;
        }

        .search-box:focus-within .search-icon {
            color: var(--primary-color);
        }

        /* 弹窗样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(8px);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            animation: fadeIn 0.3s ease;
        }

        .modal-overlay.show {
            display: flex;
        }

        .modal-content {
            background: var(--bg-secondary);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            padding: 2.5rem;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            animation: slideUp 0.3s ease;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--border-color);
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-color);
            margin: 0;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.8rem;
            cursor: pointer;
            color: var(--text-light);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .modal-close:hover {
            background: var(--border-color);
            color: var(--text-color);
            transform: rotate(90deg);
        }

        .modal-body {
            margin-bottom: 2rem;
        }

        .modal-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from { 
                opacity: 0;
                transform: translateY(20px);
            }
            to { 
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div id="login-container" class="login-container">
        <div class="login-box">
            <h2>管理系统</h2>
            <input type="text" id="username" class="auth-input" placeholder="用户名（管理员功能需要）" />
            <input type="password" id="auth-code" class="auth-input" placeholder="请输入授权码" />
            <button onclick="authenticate()">登录</button>
            <small style="color: var(--text-light); margin-top: 1rem; display: block; text-align: center;">
                访问用量统计需要管理员权限
            </small>
        </div>
    </div>

    <div id="main-container" class="container" style="display: none;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
            <h1 style="margin: 0;">管理系统</h1>
            <div style="display: flex; align-items: center; gap: 1rem;">
                <span id="welcomeText" style="color: var(--text-secondary); font-weight: 500;"></span>
                <button onclick="logout()" class="secondary-button" style="padding: 0.5rem 1rem; font-size: 0.9rem;">
                    退出登录
                </button>
            </div>
        </div>
        
        <div class="nav-tabs">
            <div class="nav-tab active" onclick="switchTab('users')">用户管理</div>
            <div class="nav-tab" onclick="switchTab('usage')">用量统计</div>
            <div class="nav-tab" onclick="switchTab('cards')">卡密管理</div>
        </div>

        <div id="users-tab" class="tab-content active">
            <div class="search-container">
                <div class="search-box">
                    <input type="text" id="userSearch" class="search-input" placeholder="搜索用户名、邮箱、VIP状态..." onInput="searchUsers()">
                    <div class="search-icon">🔍</div>
                </div>
            </div>
            <div class="table-container">
                <table class="user-table">
                    <thead>
                        <tr>
                            <th onclick="sortTable('username')" class="sortable">
                                用户名 <span class="sort-icon">↕</span>
                            </th>
                            <th onclick="sortTable('createAt')" class="sortable">
                                创建时间 <span class="sort-icon">↕</span>
                            </th>
                            <th onclick="sortTable('quota.daily')" class="sortable">
                                每日配额 <span class="sort-icon">↕</span>
                            </th>
                            <th onclick="sortTable('quota.used')" class="sortable">
                                已用配额 <span class="sort-icon">↕</span>
                            </th>
                            <th onclick="sortTable('quota.resetAt')" class="sortable">
                                重置时间 <span class="sort-icon">↕</span>
                            </th>
                            <th onclick="sortTable('vip.expireAt')" class="sortable">
                                VIP到期 <span class="sort-icon">↕</span>
                            </th>
                        </tr>
                    </thead>
                    <tbody id="user-list">
                        <!-- 用户数据将通过 JavaScript 动态插入 -->
                    </tbody>
                </table>
            </div>
            <div id="loading" class="loading"></div>
        </div>

        <div id="usage-tab" class="tab-content" style="display: none;">
            <div class="usage-container">
                <div class="usage-header">
                    <h2>用户用量统计</h2>
                    <div class="usage-controls">
                        <div class="pagination-controls">
                            <button id="prevPage" class="secondary-button" onclick="loadPreviousPage()" disabled>上一页</button>
                            <span id="pageInfo" class="page-info">第 1 页</span>
                            <button id="nextPage" class="secondary-button" onclick="loadNextPage()">下一页</button>
                        </div>
                        <select id="pageSize" class="form-select" onchange="changePageSize()">
                            <option value="50">50 条/页</option>
                            <option value="100">100 条/页</option>
                            <option value="200" selected>200 条/页</option>
                        </select>
                        <button onclick="refreshUsage()" class="primary-button">刷新数据</button>
                    </div>
                </div>
                
                <div class="usage-stats">
                    <div class="stats-card">
                        <div class="stats-value" id="totalUsers">-</div>
                        <div class="stats-label">总用户数</div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-value" id="totalCharsUsed">-</div>
                        <div class="stats-label">历史总字符数</div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-value" id="monthlyCharsUsed">-</div>
                        <div class="stats-label">本月字符数</div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-value" id="vipUsersCount">-</div>
                        <div class="stats-label">VIP用户数</div>
                    </div>
                </div>

                <div class="search-container">
                    <div style="display: flex; justify-content: center; align-items: center; gap: 2rem; flex-wrap: wrap; margin-bottom: 1rem;">
                        <div class="search-box" style="margin: 0;">
                            <input type="text" id="usageSearch" class="search-input" placeholder="搜索用户名..." onInput="searchUsage()">
                            <div class="search-icon">🔍</div>
                        </div>
                        <div class="filter-controls">
                            <label style="color: var(--text-secondary); font-weight: 500; margin-right: 1rem;">筛选条件：</label>
                            <button id="filterAll" class="filter-button" onclick="setUsageFilter('all')">全部用户</button>
                            <button id="filterActive" class="filter-button active" onclick="setUsageFilter('active')">有使用记录</button>
                            <button id="filterInactive" class="filter-button" onclick="setUsageFilter('inactive')">无使用记录</button>
                        </div>
                    </div>
                </div>

                <div class="table-container">
                    <table class="user-table">
                        <thead>
                            <tr>
                                <th onclick="sortUsageTable('username')" class="sortable">
                                    用户名 <span class="sort-icon">↕</span>
                                </th>
                                <th onclick="sortUsageTable('usage.totalChars')" class="sortable">
                                    历史总字符 <span class="sort-icon">↕</span>
                                </th>
                                <th onclick="sortUsageTable('usage.monthlyChars')" class="sortable">
                                    本月字符 <span class="sort-icon">↕</span>
                                </th>
                                <th onclick="sortUsageTable('usage.monthlyResetAt')" class="sortable">
                                    月度重置时间 <span class="sort-icon">↕</span>
                                </th>
                                <th onclick="sortUsageTable('createdAt')" class="sortable">
                                    注册时间 <span class="sort-icon">↕</span>
                                </th>
                                <th onclick="sortUsageTable('vip.expireAt')" class="sortable">
                                    VIP状态 <span class="sort-icon">↕</span>
                                </th>
                            </tr>
                        </thead>
                        <tbody id="usage-list">
                            <!-- 用量数据将通过 JavaScript 动态插入 -->
                        </tbody>
                    </table>
                </div>
                <div id="usageLoading" class="loading"></div>
            </div>
        </div>

        <div id="cards-tab" class="tab-content" style="display: none;">
            <div class="tabs-inner">
                <div class="sub-nav-tabs">
                    <div class="nav-tab active" onclick="switchCardTab('generate')">生成卡密</div>
                    <div class="nav-tab" onclick="switchCardTab('query')">卡密查询</div>
                </div>

                <div id="generate-tab" class="sub-tab-content active">
                    <div class="card-container">
                        <div class="card-form">
                            <div class="form-group">
                                <label for="cardType">选择卡密类型</label>
                                <select id="cardType" class="form-select">
                                    <option value="M">月卡</option>
                                    <option value="Q">季卡</option>
                                    <option value="H">半年卡</option>
                                    <option value="PT">体验卡</option>
                                    <option value="PM">PRO月卡</option>
                                    <option value="PQ">PRO季卡</option>
                                    <option value="PH">PRO半年卡</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="cardCount">生成数量</label>
                                <input type="number" id="cardCount" class="form-input" value="1" min="1" max="50">
                                <small class="form-text">单次最多可生成 50 个卡密</small>
                            </div>
                            


                            <button onclick="generatePreview()" id="generateBtn" class="primary-button">生成卡密</button>
                        </div>

                        <div id="result" class="result-card">
                            <div class="card-result-content"></div>
                            <div class="generation-progress" style="display: none;">
                                <div class="progress-text">正在生成: <span id="progressCount">0</span>/<span id="totalCount">0</span></div>
                                <div class="progress-bar">
                                    <div class="progress-fill"></div>
                                </div>
                            </div>
                            <div class="result-actions" style="display: none;">
                                <button onclick="copyAllGenerated()" class="secondary-button">复制全部</button>
                                <div class="copy-format-selector">
                                    <button onclick="toggleCopyFormatDropdown()" class="copy-format-btn">
                                        复制格式 ▼
                                    </button>
                                    <div id="copyFormatDropdown" class="copy-format-dropdown">
                                        <div class="copy-format-option" onclick="copyAllWithFormat('codes')">
                                            <div class="copy-format-option-title">仅卡密号</div>
                                            <div class="copy-format-option-desc">每行一个卡密号</div>
                                        </div>
                                        <div class="copy-format-option" onclick="copyAllWithFormat('with-type')">
                                            <div class="copy-format-option-title">卡密+类型</div>
                                            <div class="copy-format-option-desc">卡密号 - 类型名称</div>
                                        </div>
                                        <div class="copy-format-option" onclick="copyAllWithFormat('csv')">
                                            <div class="copy-format-option-title">CSV格式</div>
                                            <div class="copy-format-option-desc">逗号分隔，含表头</div>
                                        </div>
                                        <div class="copy-format-option" onclick="copyAllWithFormat('json')">
                                            <div class="copy-format-option-title">JSON格式</div>
                                            <div class="copy-format-option-desc">结构化数据格式</div>
                                        </div>
                                    </div>
                                </div>
                                <button onclick="clearGeneratedCards()" class="secondary-button">清空结果</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="query-tab" class="sub-tab-content">
                    <div class="search-container">
                        <div class="search-box">
                            <input type="text" id="cardSearch" class="search-input" placeholder="搜索卡密号、类型、状态、使用者..." onInput="searchCards()">
                            <div class="search-icon">🔍</div>
                        </div>
                    </div>
                    <div class="table-container">
                        <table class="card-table">
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" id="selectAll" onclick="toggleSelectAll()">
                                    </th>
                                    <th onclick="sortCards('code')" class="sortable">
                                        卡密号 <span class="sort-icon">↕</span>
                                    </th>
                                    <th onclick="sortCards('type')" class="sortable">
                                        类型 <span class="sort-icon">↕</span>
                                    </th>
                                    <th onclick="sortCards('status')" class="sortable">
                                        状态 <span class="sort-icon">↕</span>
                                    </th>
                                    <th onclick="sortCards('user')" class="sortable">
                                        使用者 <span class="sort-icon">↕</span>
                                    </th>
                                    <th onclick="sortCards('activatedAt')" class="sortable">
                                        激活时间 <span class="sort-icon">↕</span>
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="card-list"></tbody>
                        </table>
                    </div>
                    <div class="card-actions">
                        <button onclick="copySelected()" class="secondary-button">复制选中</button>
                        <button onclick="exportToCSV('selected')" class="secondary-button">导出选中</button>
                        <button onclick="exportToCSV('all')" class="secondary-button">导出全部</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 卡密预览弹窗 -->
    <div id="previewModal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">预览卡密</h3>
                <button class="modal-close" onclick="cancelPreview()">&times;</button>
            </div>
            <div class="modal-body">
                <div id="previewContent"></div>
            </div>
            <div class="modal-actions">
                <button onclick="confirmGenerate()" class="primary-button">确认生成</button>
                <button onclick="cancelPreview()" class="secondary-button">取消</button>
            </div>
        </div>
    </div>

    <script>
        let currentCardCode = null;
        let isGenerating = false;
        let previewCodes = null;
        
        // 弹窗控制函数
        function showPreviewModal() {
            document.getElementById('previewModal').classList.add('show');
            document.body.style.overflow = 'hidden'; // 防止背景滚动
        }
        
        function hidePreviewModal() {
            document.getElementById('previewModal').classList.remove('show');
            document.body.style.overflow = 'auto'; // 恢复滚动
        }
        
        // 点击弹窗背景关闭弹窗
        document.getElementById('previewModal').addEventListener('click', function(e) {
            if (e.target === this) {
                cancelPreview();
            }
        });
        
        // ESC键关闭弹窗
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && document.getElementById('previewModal').classList.contains('show')) {
                cancelPreview();
            }
        });
        
        async function generatePreview() {
            const loading = document.getElementById('loading');
            loading.style.display = 'block';
            const generateBtn = document.getElementById('generateBtn');
            generateBtn.disabled = true;

            try {
                const cardType = document.getElementById('cardType').value;
                const cardCount = parseInt(document.getElementById('cardCount').value) || 1;
                const token = localStorage.getItem('auth_token');
                
                const response = await fetch('https://cardapi.aispeak.top', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({ type: cardType, preview: true })
                });

                const data = await response.json();
                if (data.success) {
                    const previewContent = document.getElementById('previewContent');
                    previewContent.innerHTML = '';
                    
                    // 初始化预览卡密数组
                    previewCodes = [];
                    for (let i = 0; i < cardCount; i++) {
                        const previewCode = data.code.replace(/\d+$/, (match) => {
                            const num = parseInt(match) + i;
                            return num.toString().padStart(match.length, '0');
                        });
                        previewCodes.push(previewCode);

                        const cardPreview = document.createElement('div');
                        cardPreview.className = 'preview-item';
                        cardPreview.innerHTML = `
                            <div class="preview-card">
                                <div class="preview-card-header">
                                    预览卡密 ${i + 1}/${cardCount}
                                </div>
                                <div class="preview-card-content">
                                    <div><strong>类型：</strong>${getCardTypeName(cardType)}</div>
                                    <input type="text" 
                                        class="preview-card-input" 
                                        value="${previewCode}"
                                        data-index="${i}"
                                        oninput="updatePreviewCode(this)"
                                    >
                                </div>
                            </div>
                        `;
                        previewContent.appendChild(cardPreview);
                    }
                    
                    showPreviewModal();
                } else {
                    alert('生成预览失败：' + data.message);
                }
            } catch (error) {
                console.error('生成预览错误:', error);
                alert('生成预览失败，请重试');
            } finally {
                loading.style.display = 'none';
                generateBtn.disabled = false;
            }
        }

        // 更新预览卡密
        function updatePreviewCode(input) {
            const index = parseInt(input.dataset.index);
            previewCodes[index] = input.value;
        }

        async function confirmGenerate() {
            if (isGenerating) return;
            
            const cardType = document.getElementById('cardType').value;
            const cardCount = parseInt(document.getElementById('cardCount').value) || 1;
            
            if (cardCount < 1 || cardCount > 50) {
                alert('请输入1-50之间的数量');
                return;
            }

            const result = document.getElementById('result');
            const progressDiv = result.querySelector('.generation-progress');
            const progressCount = document.getElementById('progressCount');
            const totalCount = document.getElementById('totalCount');
            const progressFill = result.querySelector('.progress-fill');
            const resultContent = result.querySelector('.card-result-content');

            result.style.display = 'block';
            progressDiv.style.display = 'block';
            resultContent.innerHTML = '';
            totalCount.textContent = cardCount;
            isGenerating = true;

            try {
                const token = localStorage.getItem('auth_token');
                
                for (let i = 0; i < cardCount; i++) {
                    progressCount.textContent = i + 1;
                    progressFill.style.width = `${((i + 1) / cardCount) * 100}%`;

                    try {
                        const response = await fetch('https://cardapi.aispeak.top', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${token}`
                            },
                            body: JSON.stringify({ 
                                type: cardType,
                                code: previewCodes[i]
                            })
                        });

                        const data = await response.json();
                        if (data.success) {
                            const cardDiv = document.createElement('div');
                            cardDiv.className = 'generated-card';
                            cardDiv.innerHTML = `
                                <div class="card-info">
                                    <div class="card-code">${data.code}</div>
                                    <div class="card-type">${getCardTypeName(cardType)}</div>
                                </div>
                                <button onclick="copySingleCard('${data.code}', this)" class="copy-btn" data-card-code="${data.code}" data-card-type="${cardType}">
                                    复制
                                </button>
                            `;
                            resultContent.appendChild(cardDiv);
                            resultContent.scrollTop = resultContent.scrollHeight;
                        } else {
                            throw new Error(data.message || '生成失败');
                        }

                        // 添加间隔时间
                        if (i < cardCount - 1) {
                            await new Promise(resolve => setTimeout(resolve, 1000));
                        }
                    } catch (error) {
                        console.error('生成错误:', error);
                        const cardDiv = document.createElement('div');
                        cardDiv.className = 'generated-card error';
                        cardDiv.textContent = `生成失败: ${error.message}`;
                        resultContent.appendChild(cardDiv);
                    }
                }
            } finally {
                isGenerating = false;
                progressDiv.style.display = 'none';
                hidePreviewModal();
                previewCodes = null;

                // 显示操作按钮（如果有生成的卡密）
                const generatedCards = resultContent.querySelectorAll('.generated-card:not(.error)');
                if (generatedCards.length > 0) {
                    const resultActions = result.querySelector('.result-actions');
                    resultActions.style.display = 'flex';
                }
            }
        }

        function cancelPreview() {
            hidePreviewModal();
            const result = document.getElementById('result');
            const resultActions = result.querySelector('.result-actions');
            result.style.display = 'none';
            resultActions.style.display = 'none';
            currentCardCode = null;
            previewCodes = null;
        }

        function getCardTypeName(type) {
            const types = {
                'M': '月卡',
                'Q': '季卡',
                'H': '半年卡',
                'PT': '体验卡',
                'PM': 'PRO月卡',
                'PQ': 'PRO季卡',
                'PH': 'PRO半年卡'
            };
            return types[type] || type;
        }

        // 复制单个卡密
        async function copySingleCard(cardCode, buttonElement) {
            try {
                await navigator.clipboard.writeText(cardCode);

                // 更新按钮状态
                const originalText = buttonElement.textContent;
                buttonElement.textContent = '已复制';
                buttonElement.classList.add('copied');

                // 2秒后恢复按钮状态
                setTimeout(() => {
                    buttonElement.textContent = originalText;
                    buttonElement.classList.remove('copied');
                }, 2000);

            } catch (err) {
                console.error('复制失败:', err);
                alert('复制失败，请手动复制');
            }
        }

        // 复制所有生成的卡密（仅卡密号）
        async function copyAllGenerated() {
            const resultContent = document.querySelector('.card-result-content');
            const generatedCards = resultContent.querySelectorAll('.generated-card:not(.error)');

            if (generatedCards.length === 0) {
                alert('没有可复制的卡密');
                return;
            }

            const cardCodes = Array.from(generatedCards).map(card => {
                const codeElement = card.querySelector('.card-code');
                return codeElement ? codeElement.textContent : '';
            }).filter(code => code);

            try {
                await navigator.clipboard.writeText(cardCodes.join('\n'));
                alert(`已复制 ${cardCodes.length} 个卡密到剪贴板`);
            } catch (err) {
                console.error('复制失败:', err);
                alert('复制失败，请手动复制');
            }
        }

        // 切换复制格式下拉菜单
        function toggleCopyFormatDropdown() {
            const dropdown = document.getElementById('copyFormatDropdown');
            dropdown.classList.toggle('show');
        }

        // 点击其他地方关闭下拉菜单
        document.addEventListener('click', function(event) {
            const selector = document.querySelector('.copy-format-selector');
            if (selector && !selector.contains(event.target)) {
                const dropdown = document.getElementById('copyFormatDropdown');
                dropdown.classList.remove('show');
            }
        });

        // 按不同格式复制所有卡密
        async function copyAllWithFormat(format) {
            const resultContent = document.querySelector('.card-result-content');
            const generatedCards = resultContent.querySelectorAll('.generated-card:not(.error)');

            if (generatedCards.length === 0) {
                alert('没有可复制的卡密');
                return;
            }

            const cardData = Array.from(generatedCards).map(card => {
                const codeElement = card.querySelector('.card-code');
                const typeElement = card.querySelector('.card-type');
                const copyBtn = card.querySelector('.copy-btn');

                return {
                    code: codeElement ? codeElement.textContent : '',
                    type: typeElement ? typeElement.textContent : '',
                    typeCode: copyBtn ? copyBtn.dataset.cardType : ''
                };
            }).filter(item => item.code);

            let copyText = '';
            let formatName = '';

            switch (format) {
                case 'codes':
                    copyText = cardData.map(item => item.code).join('\n');
                    formatName = '卡密号';
                    break;

                case 'with-type':
                    copyText = cardData.map(item => `${item.code} - ${item.type}`).join('\n');
                    formatName = '卡密+类型';
                    break;

                case 'csv':
                    const csvHeader = '卡密号,类型,状态';
                    const csvRows = cardData.map(item => `${item.code},${item.type},未使用`);
                    copyText = [csvHeader, ...csvRows].join('\n');
                    formatName = 'CSV格式';
                    break;

                case 'json':
                    const jsonData = cardData.map(item => ({
                        code: item.code,
                        type: item.type,
                        typeCode: item.typeCode,
                        status: 'unused',
                        generatedAt: new Date().toISOString()
                    }));
                    copyText = JSON.stringify(jsonData, null, 2);
                    formatName = 'JSON格式';
                    break;

                default:
                    copyText = cardData.map(item => item.code).join('\n');
                    formatName = '卡密号';
            }

            try {
                await navigator.clipboard.writeText(copyText);
                alert(`已复制 ${cardData.length} 个卡密（${formatName}格式）到剪贴板`);

                // 关闭下拉菜单
                const dropdown = document.getElementById('copyFormatDropdown');
                dropdown.classList.remove('show');

            } catch (err) {
                console.error('复制失败:', err);
                alert('复制失败，请手动复制');
            }
        }

        // 清空生成结果
        function clearGeneratedCards() {
            if (confirm('确定要清空所有生成的卡密吗？')) {
                const resultContent = document.querySelector('.card-result-content');
                const resultActions = document.querySelector('.result-actions');
                const result = document.getElementById('result');

                resultContent.innerHTML = '';
                resultActions.style.display = 'none';
                result.style.display = 'none';
            }
        }

        async function authenticate() {
            const authCode = document.getElementById('auth-code').value;
            const username = document.getElementById('username').value.trim();
            
            if (!authCode) {
                alert('请输入授权码');
                return;
            }
            
            try {
                const requestBody = { auth_code: authCode };
                if (username) {
                    requestBody.username = username;
                }
                
                const response = await fetch('https://cardapi.aispeak.top/auth', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });
                
                const data = await response.json();
                if (data.success) {
                    // 保存 token 和过期时间
                    localStorage.setItem('auth_token', data.token);
                    localStorage.setItem('token_expires', data.expiresAt);
                    if (username) {
                        localStorage.setItem('username', username);
                    }
                    document.getElementById('login-container').style.display = 'none';
                    document.getElementById('main-container').style.display = 'block';
                    
                    // 设置欢迎文本
                    const welcomeText = document.getElementById('welcomeText');
                    if (username) {
                        welcomeText.textContent = `欢迎，${username}`;
                    } else {
                        welcomeText.textContent = '欢迎使用管理系统';
                    }
                    
                    // 清除输入框
                    document.getElementById('auth-code').value = '';
                    document.getElementById('username').value = '';
                } else {
                    alert('授权码错误');
                }
            } catch (error) {
                console.error('认证错误:', error);
                alert('认证失败，请重试');
            }
        }

        // 检查是否已认证且未过期
        function checkAuth() {
            const token = localStorage.getItem('auth_token');
            const expires = localStorage.getItem('token_expires');
            
            if (token && expires) {
                if (Date.now() > parseInt(expires)) {
                    logout();
                    return;
                }
                document.getElementById('login-container').style.display = 'none';
                document.getElementById('main-container').style.display = 'block';
                
                // 设置欢迎文本
                const username = localStorage.getItem('username');
                const welcomeText = document.getElementById('welcomeText');
                if (username) {
                    welcomeText.textContent = `欢迎，${username}`;
                } else {
                    welcomeText.textContent = '欢迎使用管理系统';
                }
                
                // 默认加载用户列表
                loadUsers();
            }
        }

        // 退出登录
        function logout() {
            localStorage.removeItem('auth_token');
            localStorage.removeItem('token_expires');
            localStorage.removeItem('username');
            document.getElementById('login-container').style.display = 'block';
            document.getElementById('main-container').style.display = 'none';
            
            // 清理数据
            userData = [];
            filteredUserData = [];
            usageData = [];
            filteredUsageData = [];
            cardData = [];
            filteredCardData = [];
        }

        // 页面加载时检查认证状态
        window.onload = checkAuth;

        // 添加新的功能代码
        function switchTab(tabId) {
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab-content').forEach(content => {
                content.style.display = 'none';
            });

            document.querySelector(`.nav-tab[onclick="switchTab('${tabId}')"]`).classList.add('active');
            document.getElementById(`${tabId}-tab`).style.display = 'block';

            if (tabId === 'users') {
                loadUsers();
            } else if (tabId === 'usage') {
                loadUsageData();
            }
        }

        // 添加排序状态变量
        let currentSort = {
            field: null,
            direction: 'asc'
        };

        // 存储用户数据
        let userData = [];
        let filteredUserData = [];

        // 修改 loadUsers 函数
        async function loadUsers() {
            const token = localStorage.getItem('auth_token');
            const loading = document.getElementById('loading');
            loading.style.display = 'block';
            
            try {
                const response = await fetch('https://cardapi.aispeak.top/users', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                if (data.success) {
                    userData = data.users || []; // 保存用户数据
                    filteredUserData = [...userData]; // 初始化过滤数据
                    displayUsers(filteredUserData); // 显示用户数据
                } else {
                    throw new Error(data.message || '加载失败');
                }
            } catch (error) {
                console.error('加载用户列表失败:', error);
                document.getElementById('user-list').innerHTML = 
                    `<tr><td colspan="6" style="text-align: center; color: red;">加载失败: ${error.message}</td></tr>`;
            } finally {
                loading.style.display = 'none';
            }
        }

        // 添加排序函数
        function sortTable(field) {
            const th = event.currentTarget;
            
            // 清除其他列的排序状态
            document.querySelectorAll('th').forEach(header => {
                if (header !== th) {
                    header.classList.remove('sort-asc', 'sort-desc', 'active-sort');
                }
            });
            
            // 更新排序状态
            if (currentSort.field === field) {
                currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
            } else {
                currentSort.field = field;
                currentSort.direction = 'asc';
            }
            
            // 更新排序图标
            th.classList.toggle('sort-asc', currentSort.direction === 'asc');
            th.classList.toggle('sort-desc', currentSort.direction === 'desc');
            th.classList.add('active-sort');
            
            // 排序数据
            const sortedData = [...filteredUserData].sort((a, b) => {
                let valueA = getNestedValue(a, field);
                let valueB = getNestedValue(b, field);
                
                // 处理特殊字段
                if (field === 'vip.expireAt') {
                    valueA = a.vip ? a.vip.expireAt : -1;
                    valueB = b.vip ? b.vip.expireAt : -1;
                }
                
                if (valueA === valueB) return 0;
                
                const modifier = currentSort.direction === 'asc' ? 1 : -1;
                return valueA > valueB ? modifier : -modifier;
            });
            
            // 显示排序后的数据
            displayUsers(sortedData);
        }

        // 获取嵌套对象的值
        function getNestedValue(obj, path) {
            return path.split('.').reduce((current, key) => 
                current ? current[key] : undefined, obj);
        }

        // 显示用户数据
        function displayUsers(users) {
            const userList = document.getElementById('user-list');
            userList.innerHTML = '';
            
            if (users && users.length > 0) {
                users.forEach(user => {
                    const row = document.createElement('tr');
                    const createDate = new Date(user.createAt).toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                    const resetDate = new Date(user.quota.resetAt).toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                    const vipStatus = user.vip 
                        ? `<span class="status-tag vip-tag">${new Date(user.vip.expireAt).toLocaleDateString()} (${user.vip.type})</span>`
                        : `<span class="status-tag non-vip-tag">非VIP</span>`;

                    row.innerHTML = `
                        <td>${user.username}</td>
                        <td>${createDate}</td>
                        <td>${user.quota.daily}</td>
                        <td>${user.quota.used}</td>
                        <td>${resetDate}</td>
                        <td>${vipStatus}</td>
                    `;
                    userList.appendChild(row);
                });
            } else {
                userList.innerHTML = '<tr><td colspan="6" style="text-align: center;">暂无用户数据</td></tr>';
            }
        }

        // 编辑卡密
        function editCard() {
            if (!currentCardData) return;
            
            const editForm = document.querySelector('.card-edit-form');
            const editInput = document.getElementById('editCardInput');
            editInput.value = currentCardData.code;
            editForm.style.display = 'block';
        }

        // 保存编辑
        async function saveCardEdit() {
            const newCode = document.getElementById('editCardInput').value.trim();
            if (!newCode || !currentCardData) return;

            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch('https://cardapi.aispeak.top/edit-card', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        oldCode: currentCardData.code,
                        newCode: newCode,
                        type: currentCardData.type
                    })
                });

                const data = await response.json();
                if (data.success) {
                    currentCardData.code = newCode;
                    document.querySelector('.card-result-content').innerHTML = 
                        `✅ 卡密已更新：${newCode}`;
                    document.querySelector('.card-edit-form').style.display = 'none';
                } else {
                    alert('更新失败：' + data.message);
                }
            } catch (error) {
                console.error('更新错误:', error);
                alert('更新失败，请重试');
            }
        }

        // 取消编辑
        function cancelCardEdit() {
            document.querySelector('.card-edit-form').style.display = 'none';
        }

        // 复制卡密
        function copyCard() {
            if (!currentCardData) return;
            
            navigator.clipboard.writeText(currentCardData.code)
                .then(() => {
                    alert('卡密已复制到剪贴板');
                })
                .catch(err => {
                    console.error('复制失败:', err);
                    alert('复制失败，请手动复制');
                });
        }

        // 添加更新预览函数
        function updatePreview() {
            const newCode = document.getElementById('previewCardInput').value.trim();
            if (!newCode) {
                alert('卡密不能为空');
                return;
            }
            
            currentCardCode = newCode;
            const cardType = document.getElementById('cardType').value;
            const previewContent = document.getElementById('previewContent');
            
            previewContent.innerHTML = `
                <div class="preview-item">
                    <strong>卡密号：</strong>${newCode}
                </div>
                <div class="preview-item">
                    <strong>类型：</strong>${getCardTypeName(cardType)}
                </div>
            `;
        }

        // 卡密排序状态
        let cardSortState = {
            field: null,
            direction: 'asc'
        };

        // 存储卡密数据
        let cardData = [];
        let filteredCardData = [];

        // 切换卡密管理子标签页
        function switchCardTab(tab) {
            // 移除所有标签页的活动状态
            document.querySelectorAll('.sub-tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.querySelectorAll('.sub-nav-tabs .nav-tab').forEach(navTab => {
                navTab.classList.remove('active');
            });
            
            // 激活选中的标签页
            document.getElementById(`${tab}-tab`).classList.add('active');
            document.querySelector(`.sub-nav-tabs .nav-tab[onclick*="${tab}"]`).classList.add('active');
            
            // 如果切换到查询标签页，则加载卡密列表
            if (tab === 'query') {
                loadCards();
            }
        }

        // 加载卡密列表
        async function loadCards() {
            const loading = document.getElementById('loading');
            loading.style.display = 'block';
            
            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch('https://cardapi.aispeak.top/cards', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                if (data.success) {
                    cardData = data.cards;
                    filteredCardData = [...cardData]; // 初始化过滤数据
                    displayCards(filteredCardData);
                } else {
                    alert('加载卡密列表失败：' + data.message);
                }
            } catch (error) {
                console.error('加载卡密错误:', error);
                alert('加载卡密列表失败，请重试');
            } finally {
                loading.style.display = 'none';
            }
        }

        // 显示卡密列表
        function displayCards(cards) {
            const cardList = document.getElementById('card-list');
            cardList.innerHTML = '';
            
            if (cards && cards.length > 0) {
                cards.forEach(card => {
                    const row = document.createElement('tr');
                    const activatedDate = card.activatedAt ? new Date(card.activatedAt).toLocaleString() : '-';
                    const statusClass = card.status === 'unused' ? 'status-unused' : 'status-used';
                    const statusText = card.status === 'unused' ? '未使用' : '已使用';
                    
                    row.innerHTML = `
                        <td class="checkbox-cell">
                            <input type="checkbox" class="card-checkbox" value="${card.code}">
                        </td>
                        <td>${card.code}</td>
                        <td>${getCardTypeName(card.type)}</td>
                        <td><span class="status-tag ${statusClass}">${statusText}</span></td>
                        <td>${card.user || '-'}</td>
                        <td>${activatedDate}</td>
                    `;
                    cardList.appendChild(row);
                });
            } else {
                cardList.innerHTML = '<tr><td colspan="6" style="text-align: center;">暂无卡密数据</td></tr>';
            }
        }

        // 卡密排序
        function sortCards(field) {
            if (cardSortState.field === field) {
                cardSortState.direction = cardSortState.direction === 'asc' ? 'desc' : 'asc';
            } else {
                cardSortState.field = field;
                cardSortState.direction = 'asc';
            }

            // 更新排序图标
            document.querySelectorAll('.card-table th').forEach(th => {
                th.classList.remove('sort-asc', 'sort-desc', 'active-sort');
            });
            const th = document.querySelector(`.card-table th[onclick*="${field}"]`);
            th.classList.add(`sort-${cardSortState.direction}`, 'active-sort');

            // 排序数据
            const sortedCards = [...filteredCardData].sort((a, b) => {
                let valueA = a[field];
                let valueB = b[field];
                
                if (valueA === valueB) return 0;
                
                const modifier = cardSortState.direction === 'asc' ? 1 : -1;
                return valueA > valueB ? modifier : -modifier;
            });

            displayCards(sortedCards);
        }

        // 全选/取消全选
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.card-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
        }

        // 复制选中的卡密
        async function copySelected() {
            const checkboxes = document.querySelectorAll('.card-checkbox:checked');
            if (checkboxes.length === 0) {
                alert('请先选择要复制的卡密');
                return;
            }

            const selectedCodes = Array.from(checkboxes).map(cb => cb.value).join('\n');
            try {
                await navigator.clipboard.writeText(selectedCodes);
                alert('已复制选中的卡密');
            } catch (err) {
                console.error('复制失败:', err);
                alert('复制失败，请手动复制');
            }
        }

        // 导出为CSV
        function exportToCSV(type) {
            let cardsToExport = [];
            
            if (type === 'selected') {
                const checkboxes = document.querySelectorAll('.card-checkbox:checked');
                if (checkboxes.length === 0) {
                    alert('请先选择要导出的卡密');
                    return;
                }
                const selectedCodes = new Set(Array.from(checkboxes).map(cb => cb.value));
                cardsToExport = filteredCardData.filter(card => selectedCodes.has(card.code));
            } else {
                cardsToExport = filteredCardData;
            }

            if (cardsToExport.length === 0) {
                alert('没有可导出的数据');
                return;
            }

            // 准备CSV内容
            const headers = ['卡密号', '类型', '状态', '使用者', '激活时间'];
            const csvContent = [
                headers.join(','),
                ...cardsToExport.map(card => [
                    card.code,
                    getCardTypeName(card.type),
                    card.status === 'unused' ? '未使用' : '已使用',
                    card.user || '-',
                    card.activatedAt ? new Date(card.activatedAt).toLocaleString() : '-'
                ].join(','))
            ].join('\n');

            // 创建下载链接
            const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `卡密列表_${new Date().toISOString().slice(0,10)}.csv`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }

        // 用户搜索功能
        function searchUsers() {
            const searchTerm = document.getElementById('userSearch').value.toLowerCase().trim();
            
            if (!searchTerm) {
                filteredUserData = [...userData];
            } else {
                filteredUserData = userData.filter(user => {
                    const username = user.username?.toLowerCase() || '';
                    const vipType = user.vip ? user.vip.type?.toLowerCase() || '' : 'non-vip';
                    const vipStatus = user.vip ? 'vip' : 'non-vip';
                    const dailyQuota = user.quota?.daily?.toString() || '';
                    const usedQuota = user.quota?.used?.toString() || '';
                    
                    return username.includes(searchTerm) ||
                           vipType.includes(searchTerm) ||
                           vipStatus.includes(searchTerm) ||
                           dailyQuota.includes(searchTerm) ||
                           usedQuota.includes(searchTerm);
                });
            }
            
            // 重置排序状态
            currentSort = { field: null, direction: 'asc' };
            document.querySelectorAll('.user-table th').forEach(th => {
                th.classList.remove('sort-asc', 'sort-desc', 'active-sort');
            });
            
            displayUsers(filteredUserData);
        }

        // 卡密搜索功能
        function searchCards() {
            const searchTerm = document.getElementById('cardSearch').value.toLowerCase().trim();
            
            if (!searchTerm) {
                filteredCardData = [...cardData];
            } else {
                filteredCardData = cardData.filter(card => {
                    const code = card.code?.toLowerCase() || '';
                    const type = getCardTypeName(card.type)?.toLowerCase() || '';
                    const status = card.status === 'unused' ? '未使用' : '已使用';
                    const statusEn = card.status?.toLowerCase() || '';
                    const user = card.user?.toLowerCase() || '';
                    
                    return code.includes(searchTerm) ||
                           type.includes(searchTerm) ||
                           status.includes(searchTerm) ||
                           statusEn.includes(searchTerm) ||
                           user.includes(searchTerm);
                });
            }
            
            // 重置排序状态
            cardSortState = { field: null, direction: 'asc' };
            document.querySelectorAll('.card-table th').forEach(th => {
                th.classList.remove('sort-asc', 'sort-desc', 'active-sort');
            });
            
            displayCards(filteredCardData);
        }

        // ======== 用量统计功能 ========
        
        // 用量数据存储
        let usageData = [];
        let filteredUsageData = [];
        let usagePagination = {
            limit: 100,
            cursor: null,
            hasMore: false,
            currentPage: 1
        };
        let usageSort = {
            field: 'usage.totalChars',
            direction: 'desc'
        };
        
        // 筛选状态
        let usageFilter = 'active'; // 默认只显示有使用记录的用户

        // 加载用量数据
        async function loadUsageData(reset = true) {
            const loading = document.getElementById('usageLoading');
            loading.style.display = 'block';
            
            // 确保筛选按钮状态正确
            if (reset) {
                updateFilterButtonState();
            }
            
            try {
                const token = localStorage.getItem('auth_token');
                const limit = document.getElementById('pageSize').value;
                
                let url = `https://api.myaitts.com/api/admin/users/usage?limit=${limit}`;
                if (!reset && usagePagination.cursor) {
                    url += `&cursor=${usagePagination.cursor}`;
                }
                
                console.log('请求用量数据:', url);
                
                const response = await fetch(url, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    if (response.status === 403) {
                        const errorData = await response.json();
                        throw new Error(errorData.error || '需要管理员权限');
                    }
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                console.log('收到用量数据:', data);
                
                if (reset) {
                    usageData = data.users || [];
                    usagePagination.currentPage = 1;
                } else {
                    usageData = [...usageData, ...(data.users || [])];
                    usagePagination.currentPage++;
                }
                
                usagePagination.cursor = data.pagination.cursor;
                usagePagination.hasMore = data.pagination.hasMore;
                usagePagination.limit = data.pagination.limit;
                
                // 应用筛选和搜索
                applyUsageFilterAndSearch();
                
                // 应用默认排序
                if (usageSort.field && filteredUsageData.length > 0) {
                    filteredUsageData.sort((a, b) => {
                        let valueA = getNestedUsageValue(a, usageSort.field);
                        let valueB = getNestedUsageValue(b, usageSort.field);
                        
                        if (valueA === valueB) return 0;
                        
                        const modifier = usageSort.direction === 'asc' ? 1 : -1;
                        return valueA > valueB ? modifier : -modifier;
                    });
                }
                
                displayUsageData(filteredUsageData);
                updateUsageStats(filteredUsageData);
                updatePaginationControls();
                
                // 更新表头排序指示器
                updateUsageSortIndicator();
                
            } catch (error) {
                console.error('加载用量数据失败:', error);
                document.getElementById('usage-list').innerHTML = 
                    `<tr><td colspan="6" style="text-align: center; color: red;">加载失败: ${error.message}</td></tr>`;
                
                // 如果是权限错误，显示友好提示
                if (error.message.includes('管理员权限') || error.message.includes('管理员功能未配置')) {
                    document.getElementById('usage-list').innerHTML = 
                        `<tr><td colspan="6" style="text-align: center; color: red;">
                            ${error.message}<br/>
                            <small>请确保：1. 已配置ADMIN_USERS环境变量  2. 当前用户在管理员列表中</small>
                        </td></tr>`;
                }
            } finally {
                loading.style.display = 'none';
            }
        }

        // 显示用量数据
        function displayUsageData(users) {
            const usageList = document.getElementById('usage-list');
            usageList.innerHTML = '';
            
            if (users && users.length > 0) {
                users.forEach(user => {
                    const row = document.createElement('tr');
                    
                    // 格式化时间
                    const createdDate = user.createdAt ? 
                        new Date(user.createdAt).toLocaleString('zh-CN') : '-';
                    const resetDate = user.usage.monthlyResetAt ? 
                        new Date(user.usage.monthlyResetAt).toLocaleString('zh-CN') : '-';
                    
                    // VIP状态
                    const vipStatus = user.vip.expireAt > Date.now() ? 
                        `<span class="status-tag vip-tag">${new Date(user.vip.expireAt).toLocaleDateString()} (${user.vip.type})</span>` :
                        `<span class="status-tag non-vip-tag">非VIP</span>`;
                    
                    // 字符数样式
                    const totalCharsClass = getCharCountClass(user.usage.totalChars);
                    const monthlyCharsClass = getCharCountClass(user.usage.monthlyChars);
                    
                    // 行样式
                    const rowClass = getUsageRowClass(user.usage.monthlyChars);
                    if (rowClass) {
                        row.classList.add(rowClass);
                    }
                    
                    row.innerHTML = `
                        <td>${user.username}</td>
                        <td><span class="char-count ${totalCharsClass}">${formatNumber(user.usage.totalChars)}</span></td>
                        <td><span class="char-count ${monthlyCharsClass}">${formatNumber(user.usage.monthlyChars)}</span></td>
                        <td>${resetDate}</td>
                        <td>${createdDate}</td>
                        <td>${vipStatus}</td>
                    `;
                    usageList.appendChild(row);
                });
            } else {
                usageList.innerHTML = '<tr><td colspan="6" style="text-align: center;">暂无用量数据</td></tr>';
            }
        }

        // 获取字符数样式类
        function getCharCountClass(count) {
            if (count > 100000) return 'high';
            if (count > 50000) return 'medium';
            return 'low';
        }

        // 获取行样式类
        function getUsageRowClass(monthlyChars) {
            if (monthlyChars > 50000) return 'usage-high';
            if (monthlyChars > 20000) return 'usage-medium';
            if (monthlyChars > 5000) return 'usage-low';
            return '';
        }

        // 格式化数字
        function formatNumber(num) {
            return num.toLocaleString();
        }

        // 更新统计信息
        function updateUsageStats(users) {
            const totalUsers = users.length;
            const totalChars = users.reduce((sum, user) => sum + (user.usage.totalChars || 0), 0);
            const monthlyChars = users.reduce((sum, user) => sum + (user.usage.monthlyChars || 0), 0);
            const vipUsers = users.filter(user => user.vip.expireAt > Date.now()).length;
            
            // 更新统计卡片，显示当前筛选条件下的统计
            document.getElementById('totalUsers').textContent = totalUsers.toLocaleString();
            document.getElementById('totalCharsUsed').textContent = formatNumber(totalChars);
            document.getElementById('monthlyCharsUsed').textContent = formatNumber(monthlyChars);
            document.getElementById('vipUsersCount').textContent = vipUsers.toLocaleString();
            
            // 更新统计标签，显示当前筛选状态
            const filterText = getFilterText();
            const labels = document.querySelectorAll('.stats-label');
            if (labels.length >= 4) {
                labels[0].textContent = `${filterText}用户数`;
                labels[1].textContent = `${filterText}历史总字符数`;
                labels[2].textContent = `${filterText}本月字符数`;
                labels[3].textContent = `${filterText}VIP用户数`;
            }
        }

        // 获取筛选条件文本
        function getFilterText() {
            switch(usageFilter) {
                case 'active': return '活跃';
                case 'inactive': return '非活跃';
                case 'all': 
                default: return '总';
            }
        }

        // 更新筛选按钮状态
        function updateFilterButtonState() {
            document.querySelectorAll('.filter-button').forEach(btn => {
                btn.classList.remove('active');
            });
            
            const activeButton = document.getElementById(`filter${usageFilter.charAt(0).toUpperCase() + usageFilter.slice(1)}`);
            if (activeButton) {
                activeButton.classList.add('active');
            }
        }

        // 更新分页控件
        function updatePaginationControls() {
            const prevBtn = document.getElementById('prevPage');
            const nextBtn = document.getElementById('nextPage');
            const pageInfo = document.getElementById('pageInfo');
            
            prevBtn.disabled = usagePagination.currentPage <= 1;
            nextBtn.disabled = !usagePagination.hasMore;
            
            pageInfo.textContent = `第 ${usagePagination.currentPage} 页`;
        }

        // 加载下一页
        async function loadNextPage() {
            if (usagePagination.hasMore) {
                await loadUsageData(false);
            }
        }

        // 加载上一页 (简化实现，重新加载数据)
        async function loadPreviousPage() {
            // 由于KV的限制，这里简化为重新加载第一页
            if (usagePagination.currentPage > 1) {
                await loadUsageData(true);
            }
        }

        // 更改页面大小
        async function changePageSize() {
            await loadUsageData(true);
        }

        // 刷新用量数据
        async function refreshUsage() {
            await loadUsageData(true);
        }

        // 用量搜索功能
        function searchUsage() {
            applyUsageFilterAndSearch();
            
            // 应用排序
            if (usageSort.field && filteredUsageData.length > 0) {
                filteredUsageData.sort((a, b) => {
                    let valueA = getNestedUsageValue(a, usageSort.field);
                    let valueB = getNestedUsageValue(b, usageSort.field);
                    
                    if (valueA === valueB) return 0;
                    
                    const modifier = usageSort.direction === 'asc' ? 1 : -1;
                    return valueA > valueB ? modifier : -modifier;
                });
            }
            
            displayUsageData(filteredUsageData);
            updateUsageStats(filteredUsageData);
            updateUsageSortIndicator();
        }

        // 应用筛选和搜索逻辑
        function applyUsageFilterAndSearch() {
            const searchTerm = document.getElementById('usageSearch').value.toLowerCase().trim();
            
            // 先应用筛选
            let filteredByCondition = [...usageData];
            
            if (usageFilter === 'active') {
                // 有使用记录：历史总字符数 > 0
                filteredByCondition = usageData.filter(user => 
                    (user.usage?.totalChars || 0) > 0
                );
            } else if (usageFilter === 'inactive') {
                // 无使用记录：历史总字符数 = 0
                filteredByCondition = usageData.filter(user => 
                    (user.usage?.totalChars || 0) === 0
                );
            }
            // 'all' 不需要额外筛选
            
            // 再应用搜索
            if (!searchTerm) {
                filteredUsageData = filteredByCondition;
            } else {
                filteredUsageData = filteredByCondition.filter(user => {
                    const username = user.username?.toLowerCase() || '';
                    return username.includes(searchTerm);
                });
            }
        }

        // 设置筛选条件
        function setUsageFilter(filterType) {
            usageFilter = filterType;
            
            // 更新按钮状态
            document.querySelectorAll('.filter-button').forEach(btn => {
                btn.classList.remove('active');
            });
            document.getElementById(`filter${filterType.charAt(0).toUpperCase() + filterType.slice(1)}`).classList.add('active');
            
            // 重新应用筛选和搜索
            searchUsage();
        }

        // 用量表格排序
        function sortUsageTable(field) {
            const th = event.currentTarget;
            
            // 清除其他列的排序状态
            document.querySelectorAll('#usage-tab th').forEach(header => {
                if (header !== th) {
                    header.classList.remove('sort-asc', 'sort-desc', 'active-sort');
                }
            });
            
            // 更新排序状态
            if (usageSort.field === field) {
                usageSort.direction = usageSort.direction === 'asc' ? 'desc' : 'asc';
            } else {
                usageSort.field = field;
                usageSort.direction = 'asc';
            }
            
            // 更新排序图标
            th.classList.toggle('sort-asc', usageSort.direction === 'asc');
            th.classList.toggle('sort-desc', usageSort.direction === 'desc');
            th.classList.add('active-sort');
            
            // 排序数据
            const sortedData = [...filteredUsageData].sort((a, b) => {
                let valueA = getNestedUsageValue(a, field);
                let valueB = getNestedUsageValue(b, field);
                
                if (valueA === valueB) return 0;
                
                const modifier = usageSort.direction === 'asc' ? 1 : -1;
                return valueA > valueB ? modifier : -modifier;
            });
            
            // 显示排序后的数据
            displayUsageData(sortedData);
        }

        // 获取嵌套对象的值（用量数据专用）
        function getNestedUsageValue(obj, path) {
            const keys = path.split('.');
            let value = obj;
            for (const key of keys) {
                value = value ? value[key] : undefined;
            }
            return value || 0;
        }

        // 更新用量表格排序指示器
        function updateUsageSortIndicator() {
            // 清除所有排序状态
            document.querySelectorAll('#usage-tab th').forEach(th => {
                th.classList.remove('sort-asc', 'sort-desc', 'active-sort');
            });
            
            // 设置当前排序字段的指示器
            if (usageSort.field) {
                const th = document.querySelector(`#usage-tab th[onclick*="${usageSort.field}"]`);
                if (th) {
                    th.classList.add(`sort-${usageSort.direction}`, 'active-sort');
                }
            }
        }
    </script>
</body>
</html> 