export default {
  async fetch(request, env) {
    // 处理 CORS 预检请求
    if (request.method === "OPTIONS") {
      return new Response(null, {
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type, Authorization",
        }
      });
    }

    const url = new URL(request.url);
    
    try {
      // 处理认证请求
      if (url.pathname === "/auth") {
        if (request.method === "POST") {
          try {
            const data = await request.json();
            if (data.auth_code === env.AUTH_CODE) {
              const expiresAt = Date.now() + 24 * 60 * 60 * 1000;
              const token = btoa(JSON.stringify({
                exp: expiresAt,
                auth: env.AUTH_CODE,
                username: data.username || 'admin' // 支持用户名存储
              }));
              
              return new Response(JSON.stringify({
                success: true,
                token: token,
                expiresAt: expiresAt
              }), {
                headers: {
                  'Content-Type': 'application/json',
                  'Access-Control-Allow-Origin': '*'
                }
              });
            }
          } catch (error) {
            console.error('Auth error:', error);
          }
          
          return new Response(JSON.stringify({
            success: false,
            message: "授权码错误"
          }), {
            status: 401,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            }
          });
        }
      }

      // 验证 token
      const authHeader = request.headers.get('Authorization');
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        throw new Error("未授权访问");
      }

      const token = authHeader.split(' ')[1];
      const tokenData = JSON.parse(atob(token));
      
      if (Date.now() > tokenData.exp) {
        throw new Error("token已过期");
      }

      if (tokenData.auth !== env.AUTH_CODE) {
        throw new Error("无效的token");
      }

      // 管理员权限检查函数
      const checkAdminPermission = (username) => {
        console.log('[ADMIN-CHECK] 检查管理员权限 for:', username);
        
        const adminUsers = env.ADMIN_USERS;
        if (!adminUsers) {
          console.log('[ADMIN-CHECK] 管理员功能未配置');
          return false;
        }
        
        const adminList = adminUsers.split(',').map(u => u.trim());
        const isAdmin = adminList.includes(username);
        console.log('[ADMIN-CHECK] 管理员列表:', adminList, '用户:', username, '结果:', isAdmin);
        return isAdmin;
      };

      // 处理管理员全局统计接口
      if (url.pathname === "/api/admin/users/stats" && request.method === "GET") {
        console.log('[ADMIN-API] 管理员全局统计请求');

        // 从token中提取用户名进行权限检查
        const username = tokenData.username || tokenData.user;
        if (!username || !checkAdminPermission(username)) {
          console.log('[ADMIN-API] 权限检查失败');
          return new Response(JSON.stringify({
            error: env.ADMIN_USERS ? "需要管理员权限" : "管理员功能未配置",
            code: "ADMIN_PERMISSION_DENIED"
          }), {
            status: 403,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            }
          });
        }

        try {
          console.log('[ADMIN-API] 开始计算全局统计数据');

          // 获取所有用户列表
          const { keys } = await env['tts-users'].list();

          if (!keys || !Array.isArray(keys)) {
            throw new Error('无法获取用户列表');
          }

          console.log('[ADMIN-API] 总用户数量:', keys.length);

          // 初始化统计变量
          let totalUsers = 0;
          let totalCharsUsed = 0;
          let monthlyCharsUsed = 0;
          let vipUsersCount = 0;

          // 并行获取用户数据并计算统计
          const userPromises = keys.map(async (key) => {
            try {
              const userData = await env['tts-users'].get(key.name);
              if (!userData) return null;

              const user = JSON.parse(userData);

              // 检查并处理月度重置（只读操作）
              let monthlyChars = user.usage?.monthlyChars || 0;
              let monthlyResetAt = user.usage?.monthlyResetAt || 0;

              const now = Date.now();
              if (monthlyResetAt && now > monthlyResetAt) {
                monthlyChars = 0; // 重置后为0
              }

              return {
                totalChars: user.usage?.totalChars || 0,
                monthlyChars: monthlyChars,
                isVip: user.vip && user.vip.expireAt > now
              };
            } catch (error) {
              console.error(`[ADMIN-API] 处理用户 ${key.name} 统计数据错误:`, error);
              return null;
            }
          });

          // 等待所有用户数据处理完成
          const userStats = await Promise.all(userPromises);

          // 计算全局统计
          userStats.forEach(stat => {
            if (stat) {
              totalUsers++;
              totalCharsUsed += stat.totalChars;
              monthlyCharsUsed += stat.monthlyChars;
              if (stat.isVip) {
                vipUsersCount++;
              }
            }
          });

          const globalStats = {
            totalUsers,
            totalCharsUsed,
            monthlyCharsUsed,
            vipUsersCount
          };

          console.log('[ADMIN-API] 全局统计计算完成:', globalStats);

          return new Response(JSON.stringify({
            stats: globalStats,
            timestamp: Date.now()
          }), {
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            }
          });

        } catch (error) {
          console.error('[ADMIN-API] 计算全局统计错误:', error);
          return new Response(JSON.stringify({
            error: "服务器内部错误",
            code: "INTERNAL_ERROR"
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            }
          });
        }
      }

      // 处理管理员用量查询接口
      if (url.pathname === "/api/admin/users/usage" && request.method === "GET") {
        console.log('[ADMIN-API] 管理员用量查询请求');

        // 从token中提取用户名进行权限检查
        const username = tokenData.username || tokenData.user;
        if (!username || !checkAdminPermission(username)) {
          console.log('[ADMIN-API] 权限检查失败');
          return new Response(JSON.stringify({
            error: env.ADMIN_USERS ? "需要管理员权限" : "管理员功能未配置",
            code: "ADMIN_PERMISSION_DENIED"
          }), {
            status: 403,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            }
          });
        }

        try {
          const searchParams = new URL(request.url).searchParams;
          const limit = Math.min(parseInt(searchParams.get('limit') || '100'), 1000);
          const cursor = searchParams.get('cursor');

          console.log('[ADMIN-API] 查询参数 - limit:', limit, 'cursor:', cursor);

          // 获取用户列表
          const listOptions = cursor ? { cursor, limit } : { limit };
          const { keys, cursor: nextCursor, list_complete } = await env['tts-users'].list(listOptions);

          if (!keys || !Array.isArray(keys)) {
            throw new Error('无法获取用户列表');
          }

          console.log('[ADMIN-API] 获取到用户数量:', keys.length);

          // 并行获取用户数据
          const users = await Promise.all(
            keys.map(async (key) => {
              try {
                const userData = await env['tts-users'].get(key.name);
                if (!userData) return null;

                const user = JSON.parse(userData);
                
                // 检查并处理月度重置（只读操作）
                let monthlyChars = user.usage?.monthlyChars || 0;
                let monthlyResetAt = user.usage?.monthlyResetAt || 0;
                
                const now = Date.now();
                if (monthlyResetAt && now > monthlyResetAt) {
                  // 计算下一个重置时间
                  const resetDate = new Date(monthlyResetAt);
                  resetDate.setUTCMonth(resetDate.getUTCMonth() + 1);
                  monthlyResetAt = resetDate.getTime();
                  monthlyChars = 0; // 重置后为0
                }

                return {
                  username: key.name,
                  usage: {
                    totalChars: user.usage?.totalChars || 0,
                    monthlyChars: monthlyChars,
                    monthlyResetAt: monthlyResetAt
                  },
                  createdAt: user.createdAt || 0,
                  vip: {
                    expireAt: user.vip?.expireAt || 0,
                    type: user.vip?.type || null
                  }
                };
              } catch (error) {
                console.error(`[ADMIN-API] 处理用户 ${key.name} 数据错误:`, error);
                return null;
              }
            })
          );

          // 过滤掉空值
          const validUsers = users.filter(user => user !== null);

          const response = {
            users: validUsers,
            pagination: {
              limit: limit,
              hasMore: !list_complete,
              cursor: nextCursor || null,
              total: validUsers.length
            },
            timestamp: Date.now()
          };

          console.log('[ADMIN-API] 返回用户数量:', validUsers.length, '还有更多:', !list_complete);

          return new Response(JSON.stringify(response), {
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            }
          });

        } catch (error) {
          console.error('[ADMIN-API] 查询用量数据错误:', error);
          return new Response(JSON.stringify({
            error: "服务器内部错误",
            code: "INTERNAL_ERROR"
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            }
          });
        }
      }

      // 处理VIP状态管理接口
      if (url.pathname.match(/^\/api\/admin\/users\/([^\/]+)\/vip$/) && request.method === "PUT") {
        console.log('[ADMIN-API] VIP状态管理请求');

        const username = url.pathname.split('/')[4];

        // 从token中提取用户名进行权限检查
        const adminUsername = tokenData.username || tokenData.user;
        if (!adminUsername || !checkAdminPermission(adminUsername)) {
          console.log('[ADMIN-API] VIP管理权限检查失败');
          return new Response(JSON.stringify({
            success: false,
            message: env.ADMIN_USERS ? "需要管理员权限" : "管理员功能未配置",
            code: "ADMIN_PERMISSION_DENIED"
          }), {
            status: 403,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            }
          });
        }

        try {
          const requestData = await request.json();
          const { action, vipType, expireAt, extendDays } = requestData;

          console.log('[ADMIN-API] VIP操作请求:', { username, action, vipType, expireAt, extendDays });

          // 验证操作类型
          if (!['set', 'extend', 'remove'].includes(action)) {
            return new Response(JSON.stringify({
              success: false,
              message: "无效的操作类型"
            }), {
              status: 400,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
              }
            });
          }

          // 获取用户数据
          const userData = await env['tts-users'].get(username);
          if (!userData) {
            return new Response(JSON.stringify({
              success: false,
              message: "用户不存在"
            }), {
              status: 404,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
              }
            });
          }

          const user = JSON.parse(userData);
          const originalVip = user.vip ? { ...user.vip } : null;

          // 根据操作类型更新VIP状态
          switch (action) {
            case 'set':
              if (!vipType || !expireAt) {
                return new Response(JSON.stringify({
                  success: false,
                  message: "设置VIP需要提供类型和到期时间"
                }), {
                  status: 400,
                  headers: {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                  }
                });
              }

              user.vip = {
                type: vipType,
                expireAt: expireAt
              };
              break;

            case 'extend':
              if (!extendDays || extendDays <= 0) {
                return new Response(JSON.stringify({
                  success: false,
                  message: "延长VIP需要提供有效的天数"
                }), {
                  status: 400,
                  headers: {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                  }
                });
              }

              const currentExpire = user.vip?.expireAt || Date.now();
              const newExpire = Math.max(currentExpire, Date.now()) + (extendDays * 24 * 60 * 60 * 1000);
              user.vip = {
                type: user.vip?.type || vipType || 'extended',
                expireAt: newExpire
              };
              break;

            case 'remove':
              delete user.vip;
              break;
          }

          // 保存更新后的用户数据
          await env['tts-users'].put(username, JSON.stringify(user));

          console.log('[ADMIN-API] VIP状态更新成功:', {
            username,
            action,
            originalVip,
            newVip: user.vip
          });

          // 返回更新后的用户信息
          const updatedUser = {
            username: username,
            createAt: user.createdAt,
            quota: {
              daily: user.quota?.daily || 0,
              used: user.quota?.used || 0,
              resetAt: user.quota?.resetAt || 0
            },
            vip: user.vip ? {
              expireAt: user.vip.expireAt,
              type: user.vip.type
            } : null
          };

          return new Response(JSON.stringify({
            success: true,
            message: "VIP状态更新成功",
            user: updatedUser
          }), {
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            }
          });

        } catch (error) {
          console.error('[ADMIN-API] VIP状态更新失败:', error);
          return new Response(JSON.stringify({
            success: false,
            message: "更新失败: " + error.message
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            }
          });
        }
      }

      // 处理获取用户列表的请求
      if (url.pathname === "/users" && request.method === "GET") {
        let users = [];
        
        try {
          const { keys } = await env['tts-users'].list();
          
          if (!keys || !Array.isArray(keys)) {
            throw new Error('无法获取用户列表');
          }

          // 使用 Promise.all 并行处理所有用户数据
          users = await Promise.all(
            keys.map(async (key) => {
              try {
                const userData = await env['tts-users'].get(key.name);
                if (userData) {
                  const user = JSON.parse(userData);
                  return {
                    username: key.name,
                    createAt: user.createdAt,
                    quota: {
                      daily: user.quota?.daily || 0,
                      used: user.quota?.used || 0,
                      resetAt: user.quota?.resetAt || 0
                    },
                    vip: user.vip ? {
                      expireAt: user.vip.expireAt,
                      type: user.vip.type
                    } : null
                  };
                }
                return null;
              } catch (error) {
                console.error(`Error processing user ${key.name}:`, error);
                return null;
              }
            })
          );

          // 过滤掉空值
          users = users.filter(user => user !== null);

          return new Response(JSON.stringify({
            success: true,
            users: users
          }), {
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            }
          });
        } catch (error) {
          console.error('Error fetching users:', error);
          return new Response(JSON.stringify({
            success: false,
            message: error.message
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            }
          });
        }
      }

      // 处理卡密查询请求
      if (url.pathname === "/cards" && request.method === "GET") {
        try {
          // 获取所有卡密 - 使用 KV_STORE 而不是 tts-cards
          const { keys } = await env.KV_STORE.list({ prefix: 'card:' });
          let cards = [];

          // 使用 Promise.all 并行处理所有卡密数据
          const cardPromises = keys.map(async (key) => {
            const cardData = await env.KV_STORE.get(key.name);
            if (cardData) {
              const card = JSON.parse(cardData);
              return {
                code: card.c,
                type: card.t,
                status: card.s,
                user: card.u,
                activatedAt: card.a
              };
            }
            return null;
          });

          cards = (await Promise.all(cardPromises)).filter(card => card !== null);

          // 获取已使用卡密的用户用量信息
          const usedCards = cards.filter(card => card.status === 'used' && card.user);
          let usageMap = new Map();

          if (usedCards.length > 0) {
            try {
              // 提取唯一的用户名
              const usernames = [...new Set(usedCards.map(card => card.user))];

              // 并行获取用户用量数据
              const usagePromises = usernames.map(async (username) => {
                try {
                  // 添加user:前缀，因为用户数据存储在tts-users中的格式是user:username
                  const userKey = username.startsWith('user:') ? username : `user:${username}`;
                  console.log(`[CARD-USAGE] 查询用户用量: ${userKey}`);

                  const userData = await env['tts-users'].get(userKey);
                  if (userData) {
                    const user = JSON.parse(userData);
                    return {
                      username: username, // 返回原始用户名作为key
                      totalChars: user.usage?.totalChars || 0
                    };
                  }
                  return null;
                } catch (error) {
                  console.error(`获取用户 ${username} 用量数据失败:`, error);
                  return null;
                }
              });

              const usageResults = await Promise.all(usagePromises);

              // 创建用户名到用量的映射
              usageResults.forEach(result => {
                if (result) {
                  usageMap.set(result.username, result.totalChars);
                }
              });
            } catch (error) {
              console.error('获取用量数据失败:', error);
              // 继续返回卡密数据，不因为用量查询失败而影响基本功能
            }
          }

          // 为卡密数据添加用量信息
          const cardsWithUsage = cards.map(card => ({
            ...card,
            totalChars: card.status === 'used' && card.user
              ? usageMap.get(card.user) || 0
              : undefined
          }));

          return new Response(JSON.stringify({
            success: true,
            cards: cardsWithUsage
          }), {
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            }
          });
        } catch (error) {
          console.error('获取卡密列表错误:', error);
          return new Response(JSON.stringify({
            success: false,
            message: error.message || "获取卡密列表失败"
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            }
          });
        }
      }

      // 处理删除卡密请求
      if (url.pathname === "/delete-card" && (request.method === "POST" || request.method === "DELETE")) {
        try {
          const data = await request.json();
          const cardCode = data.code;

          if (!cardCode) {
            return new Response(JSON.stringify({
              success: false,
              message: "卡密代码不能为空"
            }), {
              status: 400,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
              }
            });
          }

          // 检查卡密是否存在
          const existingCard = await env.KV_STORE.get(`card:${cardCode}`);
          if (!existingCard) {
            return new Response(JSON.stringify({
              success: false,
              message: "卡密不存在"
            }), {
              status: 404,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
              }
            });
          }

          // 执行删除操作
          console.log('Deleting card:', cardCode);
          await env.KV_STORE.delete(`card:${cardCode}`);
          console.log('Card deleted successfully');

          return new Response(JSON.stringify({
            success: true,
            message: "卡密删除成功",
            code: cardCode
          }), {
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            }
          });

        } catch (error) {
          console.error('Error deleting card:', error);
          return new Response(JSON.stringify({
            success: false,
            message: error.message || "删除卡密失败"
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            }
          });
        }
      }

      // 处理卡密生成请求
      if (request.method === "POST") {
        const data = await request.json();
        const quantity = data.quantity || 1; // 支持批量生成
        const packageType = data.packageType || data.type || "M"; // 优先使用packageType，兼容type参数

        // 获取北京时间
        const now = new Date(new Date().getTime() + 8 * 60 * 60 * 1000);

        // 如果是预览请求，直接返回生成的码
        if (data.preview) {
          const cardCode = data.code || now.getUTCFullYear().toString() +
            String(now.getUTCMonth() + 1).padStart(2, '0') +
            String(now.getUTCDate()).padStart(2, '0') +
            String(now.getUTCHours()).padStart(2, '0') +
            String(now.getUTCMinutes()).padStart(2, '0');

          return new Response(JSON.stringify({
            success: true,
            code: cardCode
          }), {
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            }
          });
        }

        try {
          const generatedCards = [];

          // 批量生成卡密
          for (let i = 0; i < quantity; i++) {
            // 为每个卡密生成唯一的时间戳
            const cardTime = new Date(now.getTime() + i * 1000); // 每个卡密间隔1秒
            const cardCode = data.code || cardTime.getUTCFullYear().toString() +
              String(cardTime.getUTCMonth() + 1).padStart(2, '0') +
              String(cardTime.getUTCDate()).padStart(2, '0') +
              String(cardTime.getUTCHours()).padStart(2, '0') +
              String(cardTime.getUTCMinutes()).padStart(2, '0') +
              String(cardTime.getUTCSeconds()).padStart(2, '0');

            const cardData = {
              c: cardCode,
              t: packageType,
              s: "unused",
              u: "",
              a: 0
            };

            // 保存到KV存储
            console.log('Saving card:', cardCode);
            await env.KV_STORE.put(`card:${cardCode}`, JSON.stringify(cardData));

            generatedCards.push({
              code: cardCode,
              packageType: packageType
            });
          }

          console.log(`Successfully generated ${generatedCards.length} cards`);

          // 如果是单个生成，保持旧格式兼容性
          if (quantity === 1) {
            return new Response(JSON.stringify({
              success: true,
              code: generatedCards[0].code
            }), {
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
              }
            });
          }

          // 批量生成返回新格式
          return new Response(JSON.stringify({
            success: true,
            generated: generatedCards.length,
            requested: quantity,
            cards: generatedCards
          }), {
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            }
          });
        } catch (error) {
          // 添加错误处理
          console.error('Error saving to KV:', error);
          return new Response(JSON.stringify({ success: false, error: error.message }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            }
          });
        }
      }

      return new Response(JSON.stringify({
        success: false,
        message: "不支持的请求方法"
      }), { 
        status: 405,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });

    } catch (error) {
      return new Response(JSON.stringify({
        success: false,
        message: error.message
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }
  }
}; 