# 管理员用量查询功能 - 环境变量配置说明

## 🔧 必需的环境变量配置

为了启用管理员用量查询功能，需要在Cloudflare Workers控制台中配置以下环境变量：

### ADMIN_USERS
设置管理员用户列表，用逗号分隔多个管理员用户名。

**示例配置：**
```
变量名: ADMIN_USERS
变量值: admin,superuser,manager
```

## 📝 配置步骤

1. **登录Cloudflare Dashboard**
   - 访问：https://dash.cloudflare.com/
   - 选择你的账户

2. **进入Workers控制台**
   - 点击左侧菜单的 "Workers & Pages"
   - 找到你的tts-cards Worker

3. **配置环境变量**
   - 点击Worker名称进入详情页
   - 点击 "Settings" 标签
   - 点击 "Variables" 
   - 点击 "Add variable" 按钮

4. **添加ADMIN_USERS变量**
   - Variable name: `ADMIN_USERS`
   - Value: `admin,superuser,manager` (替换为你的管理员用户名列表)
   - 点击 "Save and deploy"

## 🚀 功能验证

1. **重新部署Worker**
   ```bash
   npx wrangler deploy
   ```

2. **登录管理系统**
   - 使用配置的管理员用户名登录
   - 例如：用户名填写 `admin`，授权码填写正确的AUTH_CODE

3. **访问用量统计**
   - 登录后点击 "用量统计" 标签页
   - 如果配置正确，应该能看到所有用户的用量数据

## ⚠️ 注意事项

- **用户名必须匹配**：登录时输入的用户名必须在ADMIN_USERS列表中
- **大小写敏感**：用户名区分大小写
- **空格处理**：系统会自动去除用户名前后的空格
- **权限隔离**：非管理员用户无法访问用量统计功能

## 🔍 故障排除

### 问题1：显示"管理员功能未配置"
**解决方案：**
- 检查是否正确设置了ADMIN_USERS环境变量
- 确保变量名拼写正确（区分大小写）
- 重新部署Worker

### 问题2：显示"需要管理员权限"
**解决方案：**
- 确保登录时输入的用户名在ADMIN_USERS列表中
- 检查用户名是否有多余的空格
- 确认用户名大小写是否匹配

### 问题3：无法加载用量数据
**解决方案：**
- 检查Worker日志中的错误信息
- 确认KV存储`tts-users`是否正确绑定
- 验证用户数据结构是否完整

## 📊 API接口信息

新增的管理员接口：
- **接口地址**：`GET /api/admin/users/usage`
- **权限要求**：需要管理员权限
- **分页支持**：支持limit和cursor参数
- **数据格式**：返回用户用量统计信息

## 🛡️ 安全说明

- 管理员功能只有配置的用户才能访问
- 接口返回的数据不包含敏感信息（如密码hash）
- 支持分页查询，避免一次性加载大量数据
- 所有操作都会记录在Worker日志中 