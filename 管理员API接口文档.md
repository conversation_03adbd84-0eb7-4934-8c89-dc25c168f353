# 管理员API接口文档

## 🔐 认证说明

所有管理员接口都需要在请求头中包含有效的JWT Token：

```http
Authorization: Bearer <admin_token>
```

### 🔑 获取管理员Token

管理员需要先通过普通用户登录接口获取JWT Token：

**步骤**：
1. 确保用户名在 `ADMIN_USERS` 环境变量中配置
2. 使用 `POST /api/auth/login` 接口登录
3. 获取的JWT Token即可用于管理员接口

**示例**：
```bash
# 1. 管理员登录
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin_user", "password": "admin_password"}'

# 响应示例
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "username": "admin_user"
  }
}

# 2. 使用返回的token访问管理员接口
curl -X GET http://localhost:3001/api/admin/stats \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

### 🔒 权限验证机制

**管理员权限验证**：
- 用户必须在 `ADMIN_USERS` 环境变量中配置
- 环境变量格式：`ADMIN_USERS="admin1,admin2,admin3"`

**权限验证流程**：
1. **Token验证**：验证JWT Token的有效性和签名
2. **用户身份提取**：从Token中提取用户名
3. **管理员权限检查**：验证用户名是否在ADMIN_USERS列表中
4. **访问授权**：通过验证后允许访问管理员功能

**注意事项**：
- Token过期时间：7200秒（2小时）
- 管理员列表支持多个用户，用逗号分隔
- 环境变量示例：`ADMIN_USERS="admin1,admin2,admin3"`

### ⚙️ 环境变量配置

```bash
# 管理员用户配置（必需）
ADMIN_USERS="admin1,admin2,admin3"

# JWT配置（必需）
JWT_SECRET="your-secret-key"
ACCESS_TOKEN_EXPIRE=7200

# 数据库配置（必需）
DATABASE_URL="postgresql://user:password@localhost:5432/database"
```

### 🛡️ 安全注意事项

1. **Token安全**：
   - 请妥善保管管理员Token，避免泄露
   - Token具有完整的管理员权限，可以访问所有敏感数据
   - 建议定期更换管理员密码

2. **环境配置**：
   - 生产环境中务必正确配置ADMIN_USERS
   - 避免在代码中硬编码管理员凭据
   - 使用强密码和安全的JWT_SECRET

3. **访问控制**：
   - 管理员接口仅限内部使用
   - 建议配置防火墙限制访问来源
   - 监控管理员操作日志

---

## 📋 卡密管理接口

### 1. 生成卡密

**接口地址**：`POST /api/admin/cards/generate`

**请求参数**：
```json
{
  "packageType": "PT",        // 必填：套餐类型
  "quantity": 1,              // 可选：生成数量，默认1，范围1-100
  "customCode": "optional"    // 可选：自定义32位卡密
}
```

**套餐类型说明**：
| 类型 | 名称 | 时长 | 价格 | 字符配额 |
|------|------|------|------|----------|
| `M` | 标准月套餐 | 30天 | ¥25 | 80,000字符 |
| `Q` | 标准季度套餐 | 90天 | ¥55 | 250,000字符 |
| `H` | 标准半年套餐 | 180天 | ¥99 | 550,000字符 |
| `PM` | PRO月套餐 | 30天 | ¥45 | 250,000字符 |
| `PQ` | PRO季度套餐 | 90天 | ¥120 | 800,000字符 |
| `PH` | PRO半年套餐 | 180天 | ¥220 | 2,000,000字符 |
| `PT` | 测试套餐 | 30分钟 | ¥0 | 5,000字符 |

**成功响应**：
```json
{
  "success": true,
  "generated": 1,
  "requested": 1,
  "cards": [
    {
      "code": "5ltcGRXIDHpRsaFbKGUBScKNORTlxV9Q",
      "packageType": "PT",
      "packageInfo": {
        "type": "PT",
        "duration": 1800000,
        "quotaChars": 5000,
        "price": 0,
        "description": "测试套餐"
      }
    }
  ]
}
```

**错误响应**：
```json
{
  "success": false,
  "generated": 0,
  "requested": 1,
  "error": "没有成功生成任何卡密",
  "errors": ["第1张卡密生成失败：卡密已存在"]
}
```

### 2. 获取卡密列表

**接口地址**：`GET /api/admin/cards`

**查询参数**：
```
?page=1&limit=20&status=unused&packageType=M
```

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `page` | number | 否 | 页码，默认1 |
| `limit` | number | 否 | 每页数量，默认20，最大100 |
| `status` | string | 否 | 卡密状态：`unused`、`used` |
| `packageType` | string | 否 | 套餐类型筛选 |

**成功响应**：
```json
{
  "cards": [
    {
      "id": 1,
      "code": "5ltcGRXIDHpRsaFbKGUBScKNORTlxV9Q",
      "package_type": "PT",
      "status": "unused",
      "package_info": {
        "type": "PT",
        "duration": 1800000,
        "quotaChars": 5000,
        "price": 0,
        "description": "测试套餐"
      },
      "created_at": "2024-01-15T10:30:00.000Z",
      "used_at": null,
      "used_by": null
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 50,
    "totalPages": 3,
    "hasNext": true,
    "hasPrev": false
  }
}
```

### 3. 获取可用套餐类型

**接口地址**：`GET /api/admin/cards/packages`

**成功响应**：
```json
{
  "packages": [
    {
      "type": "M",
      "description": "标准月套餐",
      "days": 30,
      "price": 25,
      "chars": 80000
    },
    {
      "type": "Q",
      "description": "标准季度套餐",
      "days": 90,
      "price": 55,
      "chars": 250000
    }
  ]
}
```

---

## 👥 用户管理接口

### 4. 获取用户列表

**接口地址**：`GET /api/admin/users`

**查询参数**：
```
?page=1&limit=20&search=username
```

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `page` | number | 否 | 页码，默认1 |
| `limit` | number | 否 | 每页数量，默认20，最大100 |
| `search` | string | 否 | 搜索用户名或邮箱 |

**成功响应**：
```json
{
  "users": [
    {
      "username": "testuser",
      "email": "<EMAIL>",
      "created_at": "2024-01-15T10:30:00.000Z",
      "updated_at": "2024-01-15T10:30:00.000Z",
      "vip_info": {
        "type": "M",
        "expireAt": 1705392600000,
        "quotaChars": 80000
      },
      "usage_stats": {
        "totalChars": 15000,
        "monthlyChars": 5000,
        "monthlyResetAt": 1704067200000
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "totalPages": 5,
    "hasNext": true,
    "hasPrev": false
  }
}
```

### 5. 获取用户详细信息

**接口地址**：`GET /api/admin/users/:username`

**路径参数**：
- `username`：用户名

**成功响应**：
```json
{
  "user": {
    "username": "testuser",
    "email": "<EMAIL>",
    "passwordHash": "hashed_password",
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z",
    "vip": {
      "type": "M",
      "expireAt": 1705392600000,
      "quotaChars": 80000
    },
    "usage": {
      "totalChars": 15000,
      "monthlyChars": 5000,
      "monthlyResetAt": 1704067200000
    }
  },
  "recentTasks": [
    {
      "task_id": "uuid-task-id",
      "status": "complete",
      "created_at": "2024-01-15T10:30:00.000Z",
      "completed_at": "2024-01-15T10:31:00.000Z"
    }
  ],
  "usedCards": [
    {
      "code": "5ltcGRXIDHpRsaFbKGUBScKNORTlxV9Q",
      "package_type": "M",
      "used_at": "2024-01-15T10:30:00.000Z"
    }
  ]
}
```

### 6. 更新用户VIP

**接口地址**：`PUT /api/admin/users/:username/vip`

**路径参数**：
- `username`：用户名

**请求参数**：
```json
{
  "type": "M",                    // VIP类型
  "expireAt": 1705392600000,      // 过期时间戳（毫秒）
  "quotaChars": 80000             // 字符配额
}
```

**成功响应**：
```json
{
  "message": "User VIP updated successfully",
  "vip": {
    "type": "M",
    "expireAt": 1705392600000,
    "quotaChars": 80000
  }
}
```

---

## 📊 用量统计接口

### 7. 获取用户配额信息

**接口地址**：`GET /api/user/quota`

**权限要求**：用户Token（非管理员专用，但管理员可调用）

**成功响应**：
```json
{
  "isVip": true,
  "expireAt": 1705392600000,
  "type": "M",
  "remainingTime": null,
  "quotaChars": 80000,
  "usedChars": 15000,
  "remainingChars": 65000,
  "usagePercentage": 18.75,
  "isLegacyUser": false,
  "monthlyChars": 5000,
  "totalChars": 15000,
  "isExpired": false
}
```

**字段说明**：
| 字段 | 类型 | 说明 |
|------|------|------|
| `quotaChars` | number | 总字符配额 |
| `usedChars` | number | 已使用字符数 |
| `remainingChars` | number | 剩余字符数 |
| `usagePercentage` | number | 使用百分比 |
| `monthlyChars` | number | 本月使用字符数 |
| `totalChars` | number | 历史总使用字符数 |
| `isLegacyUser` | boolean | 是否为老用户（老用户无配额限制） |

### 8. 获取用户详细统计

**接口地址**：`GET /api/user/stats`

**权限要求**：用户Token

**成功响应**：
```json
{
  "usage": {
    "totalChars": 15000,
    "monthlyChars": 5000,
    "monthlyResetAt": 1704067200000
  },
  "taskStats": {
    "total": 25,
    "completed": 23,
    "failed": 1,
    "processing": 1
  },
  "recentUsage": [
    {
      "date": "2024-01-15",
      "tasks": 5
    },
    {
      "date": "2024-01-14",
      "tasks": 3
    }
  ]
}
```

**字段说明**：
- `usage`：字符使用统计
- `taskStats`：任务执行统计
- `recentUsage`：最近7天使用趋势

### 9. 获取用户详细资料

**接口地址**：`GET /api/user/profile`

**权限要求**：用户Token

**成功响应**：
```json
{
  "username": "testuser",
  "email": "<EMAIL>",
  "createdAt": "2024-01-15T10:30:00.000Z",
  "vip": {
    "type": "M",
    "expireAt": 1705392600000,
    "quotaChars": 80000,
    "usedChars": 15000,
    "remainingChars": 65000,
    "usagePercentage": 18.75,
    "isLegacyUser": false,
    "isExpired": false
  },
  "usage": {
    "totalChars": 15000,
    "monthlyChars": 5000,
    "monthlyResetAt": 1704067200000
  }
}
```

---

## 📊 系统统计接口

### 10. 获取系统统计

**接口地址**：`GET /api/admin/stats`

**成功响应**：
```json
{
  "users": {
    "total_users": "150",
    "active_vip_users": "45",
    "new_users_7d": "12",
    "new_users_30d": "38"
  },
  "tasks": {
    "total_tasks": "1250",
    "completed_tasks": "1180",
    "failed_tasks": "70",
    "processing_tasks": "0",
    "tasks_24h": "45",
    "tasks_7d": "285"
  },
  "cards": {
    "total_cards": "200",
    "unused_cards": "150",
    "used_cards": "50",
    "new_cards_7d": "25"
  },
  "taskTrend": [
    {
      "date": "2024-01-15",
      "tasks": 15,
      "completed": 14,
      "failed": 1
    },
    {
      "date": "2024-01-14",
      "tasks": 12,
      "completed": 11,
      "failed": 1
    }
  ],
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

**字段说明**：
- `users`：用户相关统计
- `tasks`：任务执行统计（包含24小时和7天数据）
- `cards`：卡密使用统计
- `taskTrend`：最近7天任务趋势
- `timestamp`：统计生成时间

### 11. 获取用户使用量详情（管理员）

**接口地址**：`GET /api/admin/users/:username`

**路径参数**：
- `username`：用户名

**成功响应**：
```json
{
  "user": {
    "username": "testuser",
    "email": "<EMAIL>",
    "passwordHash": "hashed_password",
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z",
    "vip": {
      "type": "M",
      "expireAt": 1705392600000,
      "quotaChars": 80000,
      "usedChars": 15000,
      "remainingChars": 65000,
      "usagePercentage": 18.75,
      "isExpired": false
    },
    "usage": {
      "totalChars": 15000,
      "monthlyChars": 5000,
      "monthlyResetAt": 1704067200000
    }
  },
  "recentTasks": [
    {
      "task_id": "uuid-task-id",
      "status": "complete",
      "created_at": "2024-01-15T10:30:00.000Z",
      "completed_at": "2024-01-15T10:31:00.000Z"
    }
  ],
  "usedCards": [
    {
      "code": "5ltcGRXIDHpRsaFbKGUBScKNORTlxV9Q",
      "package_type": "M",
      "used_at": "2024-01-15T10:30:00.000Z"
    }
  ]
}
```

**用途说明**：
- 管理员可查看任意用户的详细使用情况
- 包含字符使用量、任务历史、卡密使用记录
- 用于用户支持和问题排查

### 12. 获取所有用户使用量概览

**接口地址**：`GET /api/admin/users`

**查询参数**：
```
?page=1&limit=20&search=username
```

**成功响应**：
```json
{
  "users": [
    {
      "username": "testuser",
      "email": "<EMAIL>",
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T10:30:00.000Z",
      "vip": {
        "type": "M",
        "expireAt": 1705392600000,
        "quotaChars": 80000,
        "usedChars": 15000,
        "isExpired": false
      },
      "usage": {
        "totalChars": 15000,
        "monthlyChars": 5000
      }
    }
  ],
  "pagination": {
    "total": 100,
    "limit": 20,
    "offset": 0,
    "hasMore": true
  }
}
```

**用途说明**：
- 批量查看所有用户的使用量概况
- 支持分页和搜索功能
- 便于管理员监控整体使用情况

---

## 🚨 错误响应格式

### 认证错误
```json
{
  "error": "Token required"
}
```

### 权限错误
```json
{
  "error": "需要管理员权限"
}
```

### 参数错误
```json
{
  "error": "套餐类型不能为空",
  "availableTypes": ["M", "Q", "H", "PM", "PQ", "PH", "PT"]
}
```

### 服务器错误
```json
{
  "error": "Internal server error"
}
```

---

## 📝 使用示例

### JavaScript/TypeScript 示例

```typescript
// 生成卡密
const generateCards = async (packageType: string, quantity: number) => {
  const response = await fetch('/api/admin/cards/generate', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${adminToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      packageType,
      quantity
    })
  });

  return await response.json();
};

// 获取卡密列表
const getCards = async (page = 1, status?: string) => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: '20'
  });

  if (status) params.append('status', status);

  const response = await fetch(`/api/admin/cards?${params}`, {
    headers: {
      'Authorization': `Bearer ${adminToken}`
    }
  });

  return await response.json();
};

// 获取用户使用量统计
const getUserUsage = async (username: string) => {
  const response = await fetch(`/api/admin/users/${username}`, {
    headers: {
      'Authorization': `Bearer ${adminToken}`
    }
  });

  return await response.json();
};

// 获取系统统计
const getSystemStats = async () => {
  const response = await fetch('/api/admin/stats', {
    headers: {
      'Authorization': `Bearer ${adminToken}`
    }
  });

  return await response.json();
};

// 获取用户配额信息（用户接口，管理员也可调用）
const getUserQuota = async (userToken: string) => {
  const response = await fetch('/api/user/quota', {
    headers: {
      'Authorization': `Bearer ${userToken}`
    }
  });

  return await response.json();
};

// 获取所有用户使用量概览
const getAllUsersUsage = async (page = 1, search?: string) => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: '50'
  });

  if (search) params.append('search', search);

  const response = await fetch(`/api/admin/users?${params}`, {
    headers: {
      'Authorization': `Bearer ${adminToken}`
    }
  });

  return await response.json();
};
```

### cURL 示例

```bash
# 生成测试卡密
curl -X POST http://localhost:3001/api/admin/cards/generate \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"packageType": "PT", "quantity": 5}'

# 获取卡密列表
curl -X GET "http://localhost:3001/api/admin/cards?page=1&limit=20&status=unused" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# 获取系统统计
curl -X GET http://localhost:3001/api/admin/stats \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# 获取特定用户的详细使用情况
curl -X GET http://localhost:3001/api/admin/users/testuser \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# 获取所有用户使用量概览
curl -X GET "http://localhost:3001/api/admin/users?page=1&limit=50" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# 获取用户配额信息（用户接口）
curl -X GET http://localhost:3001/api/user/quota \
  -H "Authorization: Bearer USER_TOKEN"

# 获取用户详细统计
curl -X GET http://localhost:3001/api/user/stats \
  -H "Authorization: Bearer USER_TOKEN"

# 获取用户详细资料
curl -X GET http://localhost:3001/api/user/profile \
  -H "Authorization: Bearer USER_TOKEN"
```

---

## 🔧 故障排除

### 常见错误及解决方案

#### 1. Token required (401)
**错误信息**：`{"error": "Token required"}`

**原因**：请求头中缺少Authorization字段

**解决方案**：
```bash
# 错误示例
curl -X GET http://localhost:3001/api/admin/stats

# 正确示例
curl -X GET http://localhost:3001/api/admin/stats \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 2. Authentication failed (401)
**错误信息**：`{"error": "Authentication failed"}`

**原因**：Token无效、过期或格式错误

**解决方案**：
1. 检查Token是否正确复制
2. 确认Token未过期（有效期2小时）
3. 重新登录获取新Token

#### 3. 需要管理员权限 (403)
**错误信息**：`{"error": "需要管理员权限"}`

**原因**：用户不在ADMIN_USERS环境变量中

**解决方案**：
1. 检查环境变量配置：`echo $ADMIN_USERS`
2. 确认用户名在管理员列表中
3. 重启服务使环境变量生效

#### 4. 管理员功能未配置
**错误信息**：`{"error": "管理员功能未配置"}`

**原因**：ADMIN_USERS环境变量为空或未设置

**解决方案**：
```bash
# 设置环境变量
export ADMIN_USERS="admin1,admin2,admin3"

# 或在.env文件中配置
echo 'ADMIN_USERS="admin1,admin2,admin3"' >> .env
```

#### 5. 用量统计数据异常
**错误现象**：用量统计显示为0或数据不准确

**可能原因**：
- 数据库中usage_stats字段为空
- 月度重置时间异常
- 老用户数据迁移问题

**解决方案**：
```bash
# 检查用户数据结构
curl -X GET http://localhost:3001/api/admin/users/username \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# 查看用户配额计算逻辑
curl -X GET http://localhost:3001/api/user/quota \
  -H "Authorization: Bearer USER_TOKEN"
```

#### 6. 配额计算错误
**错误现象**：remainingChars为负数或usagePercentage超过100%

**原因**：usedChars超过quotaChars（可能由于数据异常）

**解决方案**：
1. 检查用户VIP信息和使用量数据
2. 确认是否为老用户（isLegacyUser: true）
3. 老用户无配额限制，显示为undefined是正常的

### 调试技巧

1. **验证Token有效性**：
```bash
curl -X GET http://localhost:3001/api/auth/verify \
  -H "Authorization: Bearer YOUR_TOKEN"
```

2. **检查环境变量**：
```bash
# 查看当前配置
env | grep ADMIN_USERS
env | grep JWT_SECRET
```

3. **查看服务器日志**：
```bash
# 查看实时日志
tail -f logs/$(date +%Y-%m-%d).log

# 搜索认证相关错误
grep "ADMIN-CHECK\|Auth" logs/$(date +%Y-%m-%d).log
```
